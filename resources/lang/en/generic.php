<?php
return [
    "a" => "a",
    "absentnotificationtoolate" => "absent notification too late",
    "absentwithnotification" => "absent with notification",
    "absentwithoutnotification" => "absent without notification",
    "access" => "access",
    "accesscodes" => "accesscode|accesscodes",
    "accessgrantedallstudents" => "access has been granted to :affected student.|access has been granted to :affected students.",
    "accessgrantedforstudent" => "access has been granted to :studentname",
    "accessnotallowed" => "access not allowed",
    "accessrevokedallstudents" => "access has been revoked for :affected student.|access has been revoked for :affected students.",
    "accessrevokedforstudent" => "access revoked for :studentname",
    "accesstoken" => "access token",
    "accesstokeninvalid" => "accesstoken not valid",
    "actionaftersinglelesson" => "action after single lesson",
    "actionaftersinglelessonforstudent" => "action after single lesson for student :student|action after single lesson for students :student",
    "actionnaftertask" => ":student open task|:student open tasks",
    "actionnaftertaskpastdue" => ":student task past due date|:student tasks past due date",
    "actionnaftertriallesson" => "action after trial lesson",
    "actionnaftertriallessonforstudent" => "action after trial lesson for student :student|action after trial lesson for students :student",
    "actionneeded" => "action needed",
    "actiontutor" => "action tutor",
    "activeschoolyearavailable" => "actief schooljaar aanwezig",
    "activetutors" => "active tutors",
    "add" => "add",
    "addaccesstokens" => "add accesstokens",
    "addacourse" => "add a course",
    "addallstudents" => "add all students",
    "addallstudentswithactivecourse" => "add all students with active course",
    "adddeteexception" => "add date exception",
    "addedtaskfor" => "added task for",
    "addextraevent" => "Add an extra lesson",
    "additionallycreatetask" => "additionally create task for",
    "addlibrary" => "add student library",
    "addlocation" => "add course location",
    "addnewfile" => "add new file",
    "addnewlink" => "add new weblink",
    "addrecurrenceoption" => "add recurrence option",
    "address" => "address",
    "addressline1" => "address line 1",
    "addressline2" => "address line 2",
    "addschoolyear" => "add school year",
    "addstudenttogroup" => "add student to this group",
    "addtask" => "add task",
    "addtimeslice" => "add timeslice",
    "addtoschedule" => "add to schedule",
    "addtostudentgroup" => "add to student group",
    "addtriallessonrequest" => "add trial lesson request",
    "addtutor" => "add tutor",
    "adminactionneeded" => "admin action needed",
    "adolescents" => "adolescent|adolescents",
    "adult" => "adult",
    "adults" => "adult|adults",
    "adultthreshold" => "age adult",
    "afterthatyoucanschedule" => "subsequently, you can schedule the appointment, just like other appointments.",
    "age" => "age",
    "agegroup" => "age group",
    "alert" => "alert",
    "alerts" => "alert|alerts",
    "all" => "all",
    "allappointments" => "all appointments",
    "allday" => "all day",
    "allevents" => "all events",
    "alllocationsnofilter" => "all locations (no filter)",
    "allowedipaddressesbroadcast" => "allowed ip addresses for info broadcast",
    "allreadyyoumayclosethiswindow" => "Everything has been saved. If you are finished filling out your availablility you can close this window",
    "allregistrations" => "all registrations",
    "allseriesareselected" => "all appointment series are selected. Please click the button to filter the targeted appointment series.",
    "allstudentsareselected" => "all students are selected to receive the email. Please click the button to filter the targeted students.",
    "allstudentsingroup" => "all students in the group",
    "alltutorsnofilter" => "all tutors (no filter)",
    "alreadysigned" => "looks like this registration has already been signed",
    "analyse" => "analyse",
    "analyseworklistsforplanning" => "analyse worklists for planning",
    "analysing" => "analysing",
    "analysis" => "analysis",
    "analysisfailed" => "analysis failed",
    "and" => "and",
    "andendsafter" => "and ends after",
    "apply" => "apply",
    "applyto" => "apply to",
    "appointmentchanged" => "appointment changed",
    "appointmentofhaschanged" => "the appointment of :studentName on :datetime has changed",
    "appointments" => "appointment|appointments",
    "appointmentsdeleted" => "appointment(s) deleted",
    "appointmentsdeletefailed" => "deleting appointment(s) failed",
    "appointmentseries" => "appointment series",
    "appointmentwillbesentto" => "appointment will be sent to",
    "archive" => "archive",
    "archivecourse" => "archive course",
    "archivedcourses" => "archived courses",
    "areyousure" => "are you sure",
    "areyousuredeleteallstudents" => "are you sure you want to remove all students from this list?",
    "areyousuredeleteevent" => "are you sure you wish to delete this/these appointment(s) from the timetable?",
    "areyousuredeletestudent" => "are you sure you wish to delete this student's data from the CLASS database?",
    "areyousuredeletestudentfromgroup" => "are you sure you wish to delete this student from the studentgroup?",
    "areyousuredeletestudentgroup" => "are you sure you wish to delete this studentgroup's data from the CLASS database?",
    "areyousuredeletestudentlist" => "are you sure you want to delete this student list?",
    "areyousuresendemail" => "are you sure you wish to send this email?",
    "assignedto" => "assigned to",
    "at" => "at",
    "attachingdocumentfailed" => "attaching document failed",
    "attachment" => "attachment",
    "attendance" => "attendance",
    "attendanceoptions" => "attendance option|attendance options",
    "atutor" => "a tutor",
    "autoadd" => "automatic add",
    "autoapply" => "Apply Automatically",
    "availability" => "availability",
    "available" => "available",
    "availablecoursegroups" => "available coursegroups",
    "availablefrom" => "available from",
    "availableschoolyears" => "available school years",
    "availableto" => "available to",
    "back" => "back",
    "bankaccountname" => "bankaccount name",
    "bankaccountnumber" => "bankaccount number",
    "bankdata" => "bank data",
    "basicdata" => "basic data",
    "beatleast8characterslong" => "...be at least 8 characters long",
    "belongstoseriesofappointments" => "belongs to a series of appointments",
    "beyondenddateofregistration" => "The next date would go beyond the registration end date",
    "beyondenddateofschoolyear" => "The next date would go beyond the end date of the school year",
    "billable" => "billable",
    "birthdate" => "birthdate",
    "birthdays" => "birthdays",
    "blocked" => "blocked",
    "booklocation" => "book room/location",
    "broadcast" => "broadcast",
    "by" => "by",
    "calculated" => "calculated",
    "calendar" => "calendar",
    "calendarconflict" => "calendar conflict",
    "calendarconflicttutororlocation" => "calendar conflict tutor or location",
    "cancel" => "cancel",
    "cannotbedeleted" => "can not be deleted",
    "cantdelete" => "The option is in use and therefore cannot be removed.",
    "cantdeletecourseifstudentsaresubscribed" => "Cant delete course if students are subscribed",
    "canvasnotsupported" => "Canvas not supported! Please try some other browser",
    "change" => "change",
    "changealldatesinseries" => "change all dates in series",
    "changeappointments" => "change appointments",
    "changeevent" => "change event",
    "changepassword" => "change your password",
    "changessaved" => "changes saved",
    "changeswilleffectthefollowingcourses" => "changes will affect the following course|changes will affect the following courses",
    "changethisdateexception" => "change this date exception",
    "checking" => "checking",
    "checkingplaningstatus" => "checking planning status",
    "checklist" => "checklist",
    "checklistcompleted" => "checklist completed",
    "checklistdata" => "checklist data",
    "checklistdeleted" => "checklist deleted",
    "checklistforthisregistration" => "checklist for this registration",
    "checklistincomplete" => "checklist incomplete",
    "checklists" => "checklists",
    "children" => "child|children",
    "chooseachecklist" => "choose a checklist",
    "chooseacourse" => "choose a course",
    "choosealocation" => "choose a location",
    "chooseastudent" => "choose a student",
    "chooseatutor" => "choose a tutor",
    "choosecoursegroup" => "choose a course group",
    "chooseexistingfile" => "choose previously uploaded file (reuse)",
    "chooseexistinglink" => "choose previously registered weblink (reuse)",
    "choosestudentgroup" => "choose a student group",
    "choosestudentorgroup" => "choose a student or group",
    "choosestudents" => "choose student|choose students",
    "choosestudenttoaddtogroup" => "choose a student to add to this group",
    "choosetriallesson" => "choose a trial lesson",
    "choosetutor" => "choose tutors",
    "chosen" => "chosen",
    "chosentimeinvalidchar" => ":field time has invalid character",
    "city" => "city",
    "classeaccess" => "ClassE access",
    "classurl" => "class url",
    "CLASSwasabletoschedule" => "CLASS was able to schedule :nrofappointments appointments",
    "classyaccess" => "ClassY access",
    "clickhere" => "click here",
    "clicktochoosestudentstocreategroup" => "select all students to create a new group with",
    "clicktodelete" => "click trashcan to delete coupling",
    "clicktoedit" => "click here to edit",
    "clicktoopencourseeditpage" => "click here to open this course's edit page",
    "clicktorevealpin" => "click to reveal pincode",
    "close" => "close",
    "closed" => "closed",
    "closedtasks" => "closed tasks",
    "closedtrialrequests" => "closed trial requests",
    "closethistask" => "close this task",
    "closingdate" => "closing date",
    "color" => "color",
    "concerns" => "concerns",
    "concernswholeschool" => "concerns the whole school",
    "confirm" => "confirm",
    "confirmdeletefromstudentlist" => "Please confirm removal of the student from this list",
    "confirmed" => "confirmed",
    "confirmignorecurrentstudentschedule" => "<strong>Please confirm ignoring the current schedule of this student.</strong><br/><br/>This group has a planned day and time. Removing the student will result in a new planning for the student ignoring any planning that may already be in effect",
    "confirmmyregistration" => "confirm my registration",
    "confirmregistration" => "confirm my registration",
    "confirmresetpassword" => "confirm reset password",
    "confirmsavechangeevent" => "confirm save changed event",
    "confirmshare" => "(optional) I give permission to use media on which the above registered student can be seen or heard for social media channels to promote :school.",
    "conflictDEDates" => "conflict date",
    "conflictDEDescription" => "conflict reason",
    "conflictDescription" => "conflict description",
    "conflicts" => "conflict|conflicts",
    "conflictscalendar" => "conflicts: Calendar events",
    "conflictsstudentsnoemail" => "conflicts: Students without email",
    "conflictsstudentsnoregistration" => "conflicts: Students without registration",
    "conflictstutorsandlocations" => "conflicts: Tutors and locations",
    "conformplanning" => "conform planning",
    "connectionerror" => "conncetion error during information request",
    "contactdata" => "contact data",
    "containatleas1lcletter" => "...contain at least one lower case letter",
    "containatleas1specornr" => "...contain at least one special character and/or at least one number",
    "containatleas1ucletter" => "...contain at least one upper case letter",
    "contents" => "contents",
    "continueallstudentsignore" => "continue with all students (ignore warning)",
    "continuedfrompreviousblock" => "continued from the previousin time block",
    "continueonlyvalidstudents" => "continue with students that recently filled in their preference",
    "continuesinnextblock" => "continues in the next time block",
    "continuous" => "continuous",
    "copy" => "copy",
    "copyaddressdata" => "copy address data",
    "copybankdata" => "copy bank data",
    "copycontactdata" => "copy contact data",
    "copydatafromotherstudent" => "copy info from other student",
    "copystudentlists" => "copy student lists",
    "couldnotcreatecourseregistration" => "could not register a course because the trial lesson request did not specify one.",
    "couldnotgettemplatevariables" => "could not get template variables",
    "couldnotresetprice" => "could not reset price",
    "couldnotresettaxrate" => "could not reset tax rate",
    "couldnotsavedob" => "could not save date of birth. the user entered: :dob.",
    "couldnotsavenewprice" => "could not save new price",
    "couldnotsavenewtaxrate" => "could not save new tax rate",
    "couldntdeterminenextdatetotry" => "could not determine at which day to try again. (there's no repeat interval)",
    "couldntfindstartingdatein10" => "could not find a starting date that was not occupied within the next 10 schedule dates",
    "couldntreplacevariableintemplate" => "could not replace variable :theVariable",
    "count" => "count",
    "countimagesuploading" => ":countImages images uploading",
    "country" => "country",
    "counttargetemailaddresses" => "count target email addresses",
    "couplechecklist" => "couple checklist",
    "couplecourse" => "couple course",
    "coupledcourses" => "coupled course|coupled courses",
    "course" => "course",
    "courseadded" => "course added",
    "coursearchived" => "course archived",
    "coursedata" => "course data",
    "coursefieldscannotbechanged" => "some fields cannot be changed because students are or were registered for this course",
    "courseforyouthbuttooold" => "the course \":coursename\" is meant for youth but the student is :age.",
    "coursegroup" => "course group",
    "coursegroupdata" => "course group data",
    "coursegroupdeleted" => "course group deleted",
    "coursegroups" => "course groups",
    "coursegroupsmissing" => "course groups are missing",
    "coursehasbeenadded" => "course was associated to the student group",
    "coursehasbeenremoved" => "course was detached from the student group",
    "coursehascompletedchecklist" => "this registration has one or more checklists that have all been completed",
    "coursehasnochecklist" => "this registration has a no coupled checklist",
    "coursehasnotbeenscheduled" => "course has not been scheduled",
    "coursehasuncompletechecklist" => "this registration has a checklist that has not yet been completed",
    "courseisscheduledon" => "this course is scheduled on",
    "coursename" => "course name",
    "coursenotarchived" => "course not archived",
    "courseregistration" => "course registration",
    "courseregistrationendsafter" => "registration end date",
    "courseregistrationfor" => "course registration for",
    "courserelationships" => "course relationship|course relationships",
    "courseremoved" => "course detached",
    "courses" => "course|courses",
    "courseslist" => "courses list",
    "coursestobescheduled" => "courses to be scheduled for school year :schoolyear.<br/><small>:nrOfCourses courses to go</small>",
    "coursetaxrateadults" => "course tax (adults)",
    "coursetype" => "course type",
    "create" => "create",
    "created" => "create token",
    "createdat" => "create at",
    "createfirstcourse" => "create your first course",
    "creategroup" => "create group",
    "createnewappointment" => "plan new appointment",
    "createnewappointments" => "plan new appointments",
    "createnewmandatenumber" => "generate a new mandate number",
    "createnewstudentgroup" => "create a new student group",
    "createnewtoken" => "create new access token",
    "createpassword" => "create password",
    "createstudentwiththesevalues" => "create student with these values",
    "createtoken" => "create token",
    "createworklistsforplanning" => "create worklists for planning",
    "creatstudentfromtrialrequest" => "create student from trial lesson request",
    "current" => "current",
    "currentcourses" => "current courses",
    "currentevents" => "currently planned",
    "currentlessontime" => "current lesson time",
    "currentmessage" => "current message",
    "currentpassword" => "current password",
    "currentpasswordisrequired" => "...enter the current password",
    "currentregistrations" => "all current course registrations",
    "currentstudentgroups" => "current studentgroups for this course",
    "currentstudents" => "current students",
    "dashboard" => "dashboard",
    "dashboardoverview" => "dashboard overview",
    "datasaved" => "data saved",
    "date" => "date",
    "dateend" => "end date",
    "dateexceptiondata" => "date exception information",
    "dateexceptions" => "date exception|date exceptions",
    "dateofbirthmissing" => "date of birth is missing, added fake value!",
    "datestart" => "start date",
    "datetime" => "date / time",
    "datetime_end" => "until",
    "datetime_start" => "from",
    "datetimecreated" => "date / time created",
    "datetimeupdated" => "date / time updated",
    "dayname" => "dayname",
    "days" => "day|days",
    "dayschedule" => "day schedule",
    "daytime" => "day / time",
    "dear" => "dear",
    "defaultchecklists" => "Default checklist|Default checklists",
    "defaultpassword" => "default password",
    "defaultrecoption" => "1 hour per day and stops after 1 occurrence",
    "defaultrecoption1" => "1.5 hour every two weeks until unregistering (continuous)",
    "defaultrecoption2" => "1.5 hour per week until unregistering (continuous)",
    "defaultrecoption3" => "1 hour per week until unregistering (continuous)",
    "defaultrecoption4" => "1 hour every two weeks until unregistering (continuous)",
    "defaultrecoption5" => "45 minutes per week until unregistering (continuous)",
    "defaultrecoption6" => "30 minutes per week until unregistering (continuous)",
    "defaultrecoption7" => "",
    "defaultrecoption8" => "1.5 hour per week and stops after 4 occurrences",
    "defaulttext" => "default text",
    "defaulttrialrecurrenceoption" => "1 hour a day and ends after 1 occurrence",
    "delaccesstoken" => "delete accesstoken",
    "delallstudents" => "delete all students from list",
    "delete" => "delete",
    "deleteaccessforstudent" => "delete access for student",
    "deleteallfutureappointments" => "delete all future appointments",
    "deletechecklist" => "delete checklist",
    "deletecourse" => "delete course",
    "deletecoursefromcoursegroup" => "delete course from coursegroup",
    "deleted" => "deleted",
    "deletedateexception" => "delete date exception",
    "deletedefaultchecklist" => "delete default checklist",
    "deletefailed" => "delete failed",
    "deletelibrary" => "delete document library",
    "deletelogentry" => "delete log entry",
    "deletemessage" => "delete message",
    "deleterecurrenceoption" => "delete recurrence option",
    "deletesavedconcept" => "delete saved concept",
    "deletestudent" => "delete student",
    "deletestudentgroup" => "delete student group",
    "deletesuccessful" => "delete was successful",
    "deletetask" => "delete this task",
    "deletetemplate" => "delete this template",
    "deletethiscourse" => "delete this course",
    "deletethislogentry" => "delete this log entry",
    "deletethismessage" => "delete this message",
    "deletethisregistration" => "delete this registration",
    "deletethisstudent" => "delete this student",
    "deletethisstudentgroup" => "delete this studentgroup",
    "deletetrialrequest" => "delete this trial lesson request",
    "deletetutor" => "delete tutor",
    "delselectedstudents" => "delete selected students from list",
    "deprecated" => "deprecated",
    "description" => "description",
    "detachingdocumentfailed" => "detaching document failed",
    "details" => "details",
    "detailurl" => "event detail url",
    "devenvironment" => "devel environment",
    "didnotenterresponseforeveryday" => "You haven't entered your availability for every day yet",
    "directsearch" => "direct search",
    "docnotdeletedbutremovedfromlib" => "the document will not be deleted but it will be removed from this library",
    "documents" => "document|documents",
    "doesnotapply" => "does not apply",
    "domain" => "domain",
    "domainname" => "domain name",
    "domainsettings" => "domain settings",
    "dontmove" => "don't move",
    "download" => "download",
    "doyouwanttocontinue" => "do you want to continue",
    "doyouwanttosetadminastutor" => "do you want to use your admin account as tutor as well?",
    "drop" => "drop",
    "duedate" => "due date",
    "duocourse" => "duo lesson",
    "duolesson" => "duo lesson",
    "dutch" => "Nederlands",
    "edit" => "edit",
    "editchecklist" => "edit checklist",
    "editdateexception" => "edit date exception",
    "editdescription" => "edit description",
    "editevent" => "edit event",
    "editing" => "editing",
    "editlibrary" => "edit student library",
    "editlocation" => "edit course location",
    "editprofile" => "edit profile",
    "editrecurrenceoption" => "edit recurrence options",
    "editschoolyear" => "edit school year",
    "editstudent" => "edit student",
    "editstudentgroup" => "edit student group",
    "editstudentlist" => "edit student list",
    "edittimetable" => "edit timetable",
    "edittutor" => "save changed tutordata",
    "email" => "email",
    "emailaddress" => "email address",
    "emailcontacts" => "email contacts",
    "emaillistallstudents" => "email list of all student (with and without active course)",
    "emaillistallstudentsminimalone" => "email list of all stdents with at least 1 active course",
    "emaillistallstudentsnocourse" => "email list of all students without an active course (ex students)",
    "emaillists" => "email lists",
    "emaillog" => "email log",
    "emailsend" => "email send",
    "emailsettings" => "email settings",
    "emailstudentswithaccess" => "email students with access",
    "emailthisstudent" => "email this student",
    "emptyisendofschoolyear" => "leaving empty means: until end of school year",
    "emptyisindefinitely" => "leaving empty means: show indefinitely (sticky)",
    "enddate" => "end date",
    "enddateforcoursesubscriptionset" => "end date for course subscription set",
    "enddatenotsamedayasstartdate" => "end date is not on the same day as start date",
    "endedcourses" => "ended course|ended courses",
    "endisbeforestart" => "the end date/time lies before the start date/time",
    "endisnotinfuture" => "the end date/time lies in the past",
    "endsafter" => "ends after",
    "endsafternrofoccurrences" => "and ends after nr of times",
    "endtime" => "end time",
    "endyear" => "end year",
    "english" => "Engels",
    "enterday" => "enter day",
    "enterfromtime" => "enter from time",
    "enterimageurl" => "enter image URL",
    "entermessage" => "enter message",
    "entertimeslice" => "enter timeslice",
    "entertotime" => "enter to time",
    "error" => "error",
    "error403subtitle" => "access denied",
    "error403title" => "an error occured",
    "error404subtitle1" => "contact",
    "error404subtitle2" => "to solve this issue",
    "error503subtitle" => "service is in maintenance, we will be back soon",
    "error503title" => "service is unavailable",
    "erroraddingstudents" => "error adding students",
    "errorendatesmallerthanstartdate" => "enddate should be bigger than startdate",
    "errorfetchingappointmentsoftoday" => "error fetching appointment of today",
    "errorfetchingcoursedata" => "error fetching course data",
    "errorfetchingdomaindata" => "error fetching domain data",
    "errorfetchingfirstappointment" => "error fetching your first upcoming appointment",
    "errorgrantingaccess" => "error granting access",
    "errorhidingalert" => "error hiding alert",
    "errorloadingattendanceoptions" => "error loading attendance options",
    "errorloadingcoursegroups" => "error loading coursegroups",
    "errorloadingcourses" => "error loading courses",
    "errorloadingdata" => "error loading data",
    "errorloadinglocations" => "error loading locations",
    "errorloadingrecurrenceoptions" => "error loading recurrence options",
    "errorloadingschoolyears" => "error loading school years",
    "errorloadingstudentlist" => "error loading student list",
    "errorloadingstudentlists" => "error loading student lists",
    "errorloadingstudents" => "error loading students",
    "errorloadingtimetables" => "error loading timetables data",
    "errorloadingtutors" => "error loading tutors",
    "errorremovingstudents" => "error removing students",
    "errorretrievingalerts" => "error retrieving alerts",
    "errorrevokingaccess" => "error revoking access",
    "errorsavingdata" => "error saving data",
    "errorsavingnewdatetime" => "error saving new date / time",
    "errorsavingpin" => "error saving pin",
    "errorsavingregistration" => "error saving course registration",
    "errorsendingmultiicsfile" => "error sending ICS file",
    "errortitle" => "an error occured",
    "errorunhidingalert" => "error unhiding alert",
    "errorupdatingmandatenumber" => "error updating mandate number",
    "eventconflicts" => "calendar conflicts",
    "eventconflictswith" => "course :coursename on :eventdate conflicts with :reason",
    "events" => "event|events",
    "eventsadded" => "event added|events added",
    "eventsAndDateExceptions" => "appointments and date exceptions (layers)",
    "eventsholidaysleave" => "events, holidays and leave",
    "evenweeks" => "even weeks",
    "everyappointmentuntilandincluding" => "all appointment from this, until and including",
    "everybody" => "everybody",
    "everyday" => "every day",
    "everyone" => "everyone",
    "everyweek" => "every week",
    "expireson" => "expires on",
    "explainactivestaff" => "active staff: not blocked and no end date in the past",
    "explainactivestudents" => "active students: registration for at least one course, no end date in the past",
    "explainaddressfields" => "These fields are used to sign email.",
    "explainadultthreshold" => "At what age should the student be cosidered 'adult'? E.g. for tax reasons",
    "explainallowedipadd" => "This grants access to the info bord showing location occupation to specific IP addresses (your school). You can enter multiple addresses separated by a comma.",
    "explainarchivecourse" => "If a course is no longer relevant but has had students in the past, you can hide it from most pages by archiving it. Use the archive page under 'reports' to restore the course.",
    "explainarrowtoaddtoschedule" => "Use the arrow buttons to put your available times per day into the right column. Once you finished the list, press the save button",
    "explainatleastonecourse" => "students that have at least one active course, enddate is empty or in the future.",
    "explainattendanceoptions" => "The following options can be used by the tutor or the administrator to mark presence or absence of students. They can only be deleted if they have not been used at all.",
    "explainautoapply" => "is automatically linked when creating a new student",
    "explainbroadcastmessage" => "This is a broadcast message. It will be visible for all students using Classy",
    "explaincalendarcolor" => "The calendar color is used to color the dateexception in the calendar, both on the web and in Class. You can use this to keep the calendar in the website's theme colors.",
    "explainConflictsDEEvents" => "total number of events that fall within this date exception plus the number that has been pinned for the calendar (sticky) in the planning card. This is shown in green if all conflicts have been pinned or if there are no conflicts at all. Click the badge to see more details.",
    "explaincoursechange" => "You will be notified of price changes",
    "explaincoursegroup" => "Every course is bound to a coursegroup like \"private lessons\" or \"creativity\".<br/>If applicable use the same division you use on your website.",
    "explaincoursenottargetcourse" => "this student is following a course that leads to the course in this student group but is not the target course itself",
    "explaincourserelationships" => "These courses and trial lessons lead to this course. You can also add participation in this student group based on these courses.",
    "explaincoursetaxrateadults" => "tax rate for adult students",
    "explaincreategroups" => "drag studentnames onto each other to create a group (1) or drag names to a group (2) to add to the group",
    "explaindateexceptions" => "unplanable dates and times like holidays or planned lesson free time",
    "explaindefaultsignaturewillbeadded" => "The default signature will be added automatically.",
    "explaindeleterestrictionlibrary" => "delete is only posible if the library is not shared with students",
    "explaindeleterestrictionlocation" => "delete is only possible if the location does not appear in the timetable",
    "explaindeleterestrictionrecoption" => "delete and edit is only possible if the option is not being used in a course",
    "explaindeleterestrictionschoolyear" => "delete is only possible if there are now events registerd on the school year",
    "explaindeleterestrictiontutor" => "delete is only possible if the tutor does not appear in the timetable",
    "explaindetailurl" => "The detail url is used to link to the detail page of the event on your website. Please include https:// in your link to the details page",
    "explainfreezetimespan" => "unselect if you wish to change the timespan",
    "explainhasaccess" => "indicates if the student has access to the schedule preferrence page",
    "explainifchosentypenotfound" => "If the chosen type is not found, Class will use the first email address available, whichever type. This choice only works if the student has multiple email addresses to choose from.",
    "explainignoreforalerts" => "If this option is set, the event will be ignored in alerts on the dashboards, but it will still block the (generated) planning",
    "explainignorenovalidprefs" => "the student has not entered a preference in the past :nrofmonth months. If you ignore this warning, the student will be considered \"no preference\", meaning no planning restrictions|the students have not entered a preference in the past :nrofmonth months. If you ignore this warning, the students will be considered \"no preference\", meaning they have planning restrictions",
    "explainimageusageinemail" => "copy the url of the image to use it in your email. Use the button \"insert media\" in the editor to insert the image",
    "explaininactivestudents" => "students that are not yet or not anymore registerd on a course",
    "explainistrialcourse" => "This is a special group for courses that have a different fee (free or reduced). They are also displayed differently on the website.",
    "explainkeepscheduletimerestriction" => "If you choose this option, we will see this as your preference and will try to accommodate you.<br/>Please contact us if you wish to change your course form.",
    "explainleavesopennotrespondedyet" => "leave the access open for anyone who hasn't responded in the last :days days",
    "explainleavesopennotrespondedyetextend" => "revoke access except everyone who didn't respond in the last :days days. We consider older than :days2 days \"last year's response\", meaning \"has not responded yet\".",
    "explainmakeadminatutor" => "You can give your admin account tutor rights to combine these roles in one account. You can do so on your profile page",
    "explainminmaxgroupsize" => "if you teach this course in a group, specify the minimum and maximum group size. leave both at \"1\" for 1 on 1 tutoring. These values will be used when creating student groups.",
    "explainnewemailsignrequest" => "if you want to send the request email to another emailaddress or use another salutation you can type it here. More than one email address is possible, they need to be separated by commas",
    "explainnoaccesstoken" => "This should not happen anymore. Report this to Scolavisa to resolve this issue.",
    "explainnotthesameasenddate" => "Please not, this is different from adding an enddate! This action completely deletes the registration!",
    "explainnrofappointmentsforyear" => "number of future appointments scheduled / total number of appointments",
    "explainnrofevents" => "nr of tutoring events: 'future' / 'all' excl blockers ('all' inc blockers)",
    "explainonlyoneconcept" => "Currently there can be 1 concept on your local computer. If you open Class on another computer you will not see the saved concept! In a later version of Class, you can select mail templates here.",
    "explainonlysingleevent" => "This field can only be changed for a <em>single appointment</em>",
    "explainopennotrespondedyet" => "Open access for anyone who hasn't responded in the last :days days",
    "explainopennotrespondedyetextend" => "students have NOT filled in their preference in the last :days days. We consider older than :days2 days \"last year's response\", meaning \"has not responded yet\".",
    "explainplanblocking" => "By default, the date exception will block generated and manual planning. If you want to allow planning at the date and time of the dateexception, you can uncheck this option. This will automatically disable alerts as well",
    "explainpreferedtime" => "we will try to accomodate your indicated prefered time if possible",
    "explainpricesubadult" => "For the price list, usually a nice round number close to the price excluding  tax.",
    "explainpwreset" => "The new password will be send to the email address of the tutor. To gain access to ClassE the tutor needs a valid password.<br>If you wish to block the tutor after this action you can do so by setting the 'block' flag in the tutor's edit screen.",
    "explainreschedule" => "Please note: CLASS will only reschedule events if they are currently scheduled on the same day of week and time as this event's series original day of week and time. Previously changed appointments are no longer part of the series and will therefor not be changed by this action.",
    "explainresetpw" => "The password of the tutor will be reset to the school's default password. The tutor is required to change their password directly after logging in the next time.",
    "explainschedulethreshold" => "nr of days ago that the student may have filled out their availability, which indicates that the registerd availability of the student is no longer valid. This prevents taking last years availability as 'current'.",
    "explainsetuppage" => "CLASS needs some extra actions to be able to execute all functions.",
    "explainsignedoffstudents" => "student that previously entered a course that ended in the past. Another course for this student may still be active.",
    "explainsignrequestemail" => "Your registration is almost complete. <ul><li>By pressing the button you confirm your registration with :schoolname. Please click the button marked 'confirm my registration'.</li> <li>Your browser will open a new window (CLASS) that will allow you to check a confirm box. After that please click the Save button to send your registration to us. You will see a page confirming the correct reception of your registration.</li></ul>If the button doesn't work, please copy this link in your browser and press Enter.",
    "explainsignrequestpage" => "Here is the information that we have stored in our administration.<br/>If everything checks out, please click the selectbox and press save",
    "explainstudentcontactlabel" => "e.g. \"private\" or \"parents\"",
    "explainstudentlist" => "<span class=\"label label-primary\">&nbsp;This background&nbsp;</span> means the student is a member of that studentlist.<br/>Click on the list name to change the membership.",
    "explainstudentsindividualaccess" => "give explicitaly access to these students",
    "explainstudentsinstudentgroupdirectaccess" => "students who are member of this group get immedeate access, without explicitly having to share with them",
    "explainstudentsnocourses" => "students without an active course.",
    "explainstudentswithcoursedirectaccess" => "students registered to this (these) course(s) have immedeate access, without explicitly having to share with them",
    "explainstudentswithcoursegroupdirectaccess" => "students registered to a course in this group have immedeate access, without explicitly having to share with them",
    "explaintrialcourserelation" => "drag the courses to the trial lessons to indicate for which course the trial lesson is meant. That can be more than one course. This relation may be used when composing student groups.",
    "explaintrialgroup" => "If you set this group as trial group, you enable to expose the courses in this group to be shown as trial courses",
    "explaintrialsettrue" => "Courses in this group can be shown as trial lessons on you website",
    "explainunarchivedcourses" => "click on the course naam to un-archive the course. Then use the archive button in the course edit screen.",
    "explainusefor" => "indicate if the studentcontact should be used specifically for one or more of these goals, for instance in mailing",
    "explainvariables" => "you can use these variables in your mail template. Class will replace them with the correct values when sending the email.",
    "explainwarnbeforeadult" => "how many days before a students becomes adult do you want to see a warning on the dashboard (may change the lesson price because of taxes). Enter \"0\" to disable this warning.",
    "explainwarnbeforebirthday" => "how many days before a students birthday do you want to see a warning on the dashboard",
    "explainwordpresscode" => "you need this code to enable subscription to this event in your WordPress website. Follow the instructions on the WordPress plugin page.",
    "exporttoexcel" => "export to spreadsheet",
    "extraemailaddresses" => "extra email addresses",
    "extrainfo" => "extra information",
    "failed" => "failed",
    "failedtogetdataforregistration" => "failed to get data for registration",
    "fieldmaynotexceedlength" => "the length of the field :fieldname may not exceed :max characters",
    "filelist" => "file list",
    "filemanagement" => "file management",
    "files" => "file|files",
    "fileupload" => "file upload",
    "fillstartdattimeabove" => "fill date segment above",
    "filter" => "filter",
    "filteron" => "filter on",
    "findDocumentToLinkToLibrary" => "find an existing document to link to this library",
    "findexistingdocument" => "find a document to link to the library",
    "finish" => "finish",
    "firstchooseplandetails" => "first choose the planning details",
    "firstchoosestudentorgroup" => "first choose a student or student group",
    "firstname" => "first name",
    "firstregistration" => "first registration",
    "fixthishere" => "fix this here",
    "followingtaskswillbedeleted" => "the following task will be deleted|the following tasks will be deleted",
    "followlink" => "Follow link",
    "followuprelations" => "follow-up relationship",
    "for" => "for",
    "foradults" => "for adults",
    "forallstudents" => "for all students",
    "forcourse" => "for course",
    "forcourseparticipants" => "for course participants",
    "formatphonenumber" => "format phone number",
    "formerstudents" => "former student|former students",
    "fortheschool" => "for the school",
    "foryouth" => "for youth",
    "frequency" => "frequency",
    "from" => "from",
    "fromemail" => "from",
    "fromtimegreaterthanendtime" => "from-time is greater than end-time",
    "full" => "full",
    "functions" => "functions",
    "futureappointments" => "future appointment|future appointments",
    "futureappointmentswillbedeleted" => "please note: future appointments will be deleted!",
    "futuremessage" => "future message",
    "genericdata" => "generic data",
    "givemetutorrights" => "give me tutor rights",
    "goto" => "go to",
    "gotoalertpage" => "go to alert page",
    "gotofulltimetable" => "go to the complete timetable",
    "gottodashboard" => "go to the dashboard",
    "grantaccess" => "grant access",
    "grantaccesstoallstudents" => "grant access to all students",
    "groupcourse" => "group course",
    "groupcourseextend" => "this course is taught in a group. Click to see the participants.",
    "groupfilter" => "group filter",
    "groupname" => "group name",
    "groups" => "group|groups",
    "groupscanonlybedeletedif" => "a group can only be deleted if no courses are coupled to it.",
    "handled" => "handled",
    "handledtrialcourserequests" => "handled trialcourse requests",
    "hasaccess" => "has access",
    "hasenddate" => "has enddate",
    "hasfutureenddate" => "future enddate",
    "hasfutureevents" => "has future events",
    "hasnoaccess" => "has no access",
    "hasnoappointmentfortrialcourse" => "trial students that have no appointment for their trial course",
    "hasnofollowupcourse" => "trial students that have no course registration beside their trial course",
    "helpcourseregistrationendsafter" => "appointments will not be planned after this date",
    "herebeappointmentsbeforesaving" => "This section will display your new appointments for you to review before they are saved.",
    "heresyourpersonalaccesstoken" => "Here is your new personal access token. This is the only time it will be shown so don't lose it! You may now use this token to make API requests.",
    "hexcolor" => "color",
    "hhmm" => "hh:mm",
    "hidden" => "hidden",
    "hide" => "hide",
    "highpriority" => "high priority",
    "holidaytable" => "holidays and leaf",
    "hours" => "hour|hours",
    "hoverclickdatetoedit" => "click the 'from' date to edit that date",
    "hoverclickseriescolortoedit" => "click the series color to edit appointments in that series",
    "howmanyeventsdoyouwanttoplan" => "how many successive events do you want to plan? '0' means the entire school year",
    "hundredmostrecententries" => "max 100 most recent emails",
    "iagree" => "I hereby confirm my registration with the above information.",
    "iagreecheckboxnotticked" => "the 'I Agree' checkbox was not ticked",
    "icon" => "icon",
    "ifagreecheckbox" => "If you agree with the information above, please check this box",
    "ifoccupiedmoveto" => "if location is occupied move to this location",
    "ignorecurrentschedule" => "ignore the current planning",
    "ignoreforalerts" => "ignore for alerts",
    "ignorethisrow" => "ignore this row",
    "imageurl" => "Image via URL",
    "importICS" => "import ICS",
    "in" => "in",
    "inactivetutors" => "inactive tutors",
    "inbox" => "inbox",
    "including" => "including",
    "incompletechecklistspresent" => "incomplete checklist(s) present",
    "individual" => "individual",
    "individualcourse" => "individual course",
    "individualcourseextend" => "this course is taught individually",
    "individualstudentstobescheduled" => "individual students to be scheduled",
    "individualstudentswanttokeepschedule" => "indivudual students that want to keep the current planning",
    "info" => "extra info",
    "ingroup" => "in group",
    "ingroupsoverwostudents" => "in groups of over two students",
    "intermediateplanning" => "intermediate planning",
    "invoiceforsinglelesson" => "invoice for single lesson",
    "iprefertokeepcurrenttime" => "I would like to keep the current time, if possible",
    "isatrialcourse" => "is a trial course",
    "isblockedforloginclasse" => "is blocked for ClassE",
    "isgroupcourse" => "is group course",
    "isnotatrialcourse" => "is not a trial course",
    "isnotblockedforloginclasse" => "is NOT blocked for ClassE",
    "isrecurringappointment" => "is recurring",
    "istrialcourse" => "is trial course",
    "istrialcoursefor" => "is trial course for",
    "istrialgroup" => "is trial group",
    "items" => "item|items",
    "itsmybirthday" => "Its :name 's birthday today!",
    "jumpto" => "jump to",
    "jumptopage" => "please confirm you want to jump to page :target",
    "keepctrlpressformultiselect" => "keep the ctrl key pressed to select multiple items",
    "keepcurrentschedule" => "keep current schedule",
    "keeptimespan" => "keep timespan",
    "kindregards" => "kind regards",
    "label" => "label",
    "language" => "en",
    "lastactivity" => "last activity",
    "lastknowndate" => "last known date",
    "lastmonth" => "last month",
    "lastname" => "last name",
    "lastnameincorrect" => "last name incorrect",
    "lasttimeemailed" => "last time emailed",
    "lasttimefilledin" => "last filled in",
    "lastupdate" => "last update",
    "leaveemptyforcontinuous" => "leave blank or '0' for 'until cancellation'",
    "lessonplanning" => "lesson planning",
    "libraries" => "library|libraries",
    "librarydata" => "library information",
    "libraryisnotshared" => "the library is not shared",
    "librarysharing" => "library sharing",
    "limitingnumberofrepeatstocoursedefinition" => "number of repetitions limited to :count due to the defined repeats of the course",
    "limitoftrialaccountreached" => "You have reached the limit of your trial account!",
    "limitoftrialaccountreachedcourse" => "You have reached the max number of courses of your trial account!",
    "limitoftrialaccountreachedstudent" => "You have reached the max number of students of your trial account!",
    "limitoftrialaccountreachedtutor" => "You have reached the max number of tutors of your trial account!",
    "link" => "link",
    "links" => "link|links",
    "list" => "list",
    "listlibraries" => "list libraries",
    "listscanonlybedeletedif" => "lists can only be deleted if no students are registered with the list",
    "liststudentsoncourses" => "List of students on courses",
    "locationdata" => "location information",
    "locationdeleted" => "location deleted",
    "locationicon" => "Location icon",
    "locationoccupied" => "course location is already occupied",
    "locations" => "course location|course locations",
    "locationsmissing" => "no course locations found",
    "locked" => "locked",
    "logbook" => "logbook",
    "logbookentry" => "logbook entry",
    "login" => "login",
    "logourl" => "logo url",
    "logout" => "logout",
    "mailallstudents" => "mail all students with access\t",
    "mailbody" => "mail body",
    "mailtemplates" => "mail templates",
    "mailtext" => "text email",
    "mailto" => "mail to",
    "manage" => "manage",
    "manageContents" => "Edit library contents",
    "manageSharing" => "Edit library sharing",
    "mandatenumber" => "mandate number",
    "mandatory" => "*",
    "maximum" => "maximum",
    "maxstudentgroupsize" => "max. student group size",
    "mayalsomakeadminatutor" => "you may also make your admin account a tutor",
    "messages" => "message|messages",
    "messagesaved" => "message saved",
    "messagesentfailed" => "sending message failed",
    "messagesentto" => "message sent to :recipient",
    "minimum" => "minimum",
    "minstudentgroupsize" => "min. student group size",
    "minutes" => "minute|minutes",
    "missingdata" => "missing data",
    "month" => "month",
    "months" => "month|months",
    "more" => "more",
    "my" => "my",
    "myappointments" => "my appointments",
    "name" => "name",
    "namecontactperson" => "name contact person",
    "new" => "new",
    "newattendanceoption" => "new attendance option",
    "newchecklist" => "new checklist",
    "newcontactitem" => "new contact item",
    "newcourse" => "new course",
    "newcoursegroup" => "new coursegroup",
    "newcourseregistration" => "new course registration",
    "newcoursetobeplanned" => "new course to be planned",
    "newdateexception" => "new date exception",
    "newdatetime" => "new date / time",
    "newdaytime" => "new day / time",
    "newdefaultchecklist" => "new default checklist",
    "neweventcreatedopentosave" => "Open the new appointment to save!",
    "newevents" => "new events",
    "newgroupforthesestudents" => "new group for these student(s)",
    "newlibrary" => "new library",
    "newlocation" => "new course location",
    "newlogentry" => "new logentry",
    "newmessage" => "new message",
    "newpassword" => "new password",
    "newpasswordisrequired" => "...enter the new password",
    "newpasswordsdonotmatch" => "...the new passwords are not the same",
    "newplanningsaved" => "new planning saved successfully",
    "newrecurrenceoption" => "new recurrenceoption",
    "newregistrations" => "new registrations",
    "newschoolyear" => "new school year",
    "newstudent" => "new student",
    "newstudentgroup" => "new student group",
    "newstudentgroupcreated" => "new student group created",
    "newstudentgroupforcourse" => "new student group for this course",
    "newstudentlist" => "new student list",
    "newtask" => "new task",
    "newtemplate" => "new template",
    "newtemplatelabel" => "label for the new template",
    "newtriallessonrequests" => "new trial lesson request|new trial lesson requests",
    "newtutor" => "new tutor",
    "next" => "next",
    "nextmonth" => "next month",
    "no" => "no",
    "noaccess" => "no access",
    "noaccestoken" => "no access token",
    "noactivecoursesnoworfuture" => "no active courses now or in the future",
    "noalerts" => "no alerts",
    "noalertsfound" => "geen alarmering gevonden",
    "noappointments" => "no appointments",
    "noappointmentsfound" => "no appointments found",
    "noappointmentstosend" => "no appointments to send",
    "nochangestosave" => "no changes to save",
    "nochecklistdefinitionsfound" => "no checklistdefinitions found",
    "nochecklistwasadded" => "no checklist coupled because this is a trial lesson request.",
    "noconflicts" => "no conflicts",
    "nocontactsfound" => "no contact data found",
    "nocoupledcourses" => "no coupled courses",
    "nocoursefound" => "no course found",
    "nocoursegroupsfound" => "no coursegroups found",
    "nocoursesfound" => "no courses found",
    "nodateexceptionsfound" => "no dateexceptions found",
    "nodefaultchcklists" => "no default checklists",
    "nodocumentsfound" => "no documents found",
    "noemailaddressfound" => "no email address found",
    "noemailaddressfoundtosendto" => "no email address found to send to",
    "noerrorsorwarnings" => "no errors or warnings",
    "nofreetriallessonevents" => "There are no trial lesson appointments without this type of task.<br/>Use one of the existing tasks to ad actions.",
    "nofurtherchangestosave" => "no further changes to save",
    "nogroupsfound" => "no studentgroups found",
    "noinputfor" => "no input for",
    "nolibrariesfound" => "no libraries found. Click on the button above to create your first document library.",
    "nologentriesfound" => "no logentry found",
    "nolongerpartofseries" => "is no longer part of the series",
    "nomessagesfound" => "no messages found",
    "nonactivestudents" => "nonactive students",
    "nonblocking" => "nonblocking",
    "none" => "none",
    "none_newplanning" => "none",
    "nooldversionsarepreserved" => "no old versions are preserved",
    "noone" => "no one",
    "nopermissionautobanktransfer" => "no permission automatic banktransfer",
    "nopreviousplanningasbase" => "no previous planning as basis",
    "noresultstoshow" => "no results to show",
    "noschoolyearavailable" => "no future school years found",
    "noschoolyearsfound" => "no school years found",
    "noseriesareselected" => "unable to send email because there are no appointment series selected",
    "nostudentgroupsfound" => "no studentgroups found",
    "nostudentlistsfound" => "no student lists found",
    "nostudentsareselected" => "unable to send email because there are no students selected",
    "nostudentsfound" => "no students found",
    "nostudentsingroup" => "no students in this group",
    "not" => "not",
    "notactive" => "overridden because of \"sticky\" appoinment setting",
    "notallseriessareselected" => "not all series are selected. only selected series will be present in the email",
    "notallstudentsareselected" => "not all students are selected. only the selected students will receive the email",
    "notasksfound" => "no tasks found",
    "notavailable" => "not available",
    "notavailableyet" => "not available yet",
    "notavalidtime" => "not a valid time",
    "notconfirmed" => "not confirmed",
    "notemplatesfound" => "no templates found",
    "notes" => "notes",
    "nothingscheduled" => "nothing scheduled",
    "notice" => "notice",
    "notimetablesfound" => "no timetables found",
    "notinplanningtable" => "These registrations have no entry in the planning table",
    "notopenyet" => "not open yet",
    "notparticipating" => "currently not participating",
    "notriallessons" => "no trial lessons",
    "notsaving" => "not saving",
    "notsigned" => "not signed",
    "notsignedbutrequestsent" => "not signed but sign request sent",
    "notsignednorequestsent" => "not signed and no sign request sent",
    "notutoringevents" => "no tutoring events",
    "notutoringeventstoshowyet" => "no tutoring events to show yet",
    "notutorsavailable" => "there are no tutors available on this day",
    "notvalid" => "niet geldig",
    "noupcomingbirthdays" => "no upcoming birthdays",
    "nowchooseacourse" => "now choose a course",
    "nrofactivestudents" => "# Active students",
    "nrofappointments" => "nr of Appointments",
    "nrofevents" => "number of events",
    "nroffutureappointmentsoftotal" => "This subscription has an active planning with :totalEvents planned appointments of which :blocked are blocked leaving a total of <strong>:totalOccuring</strong> tutoring events (<strong>:future</strong> are future appointments).",
    "nrofitems" => "nr of items",
    "nrofregistrations" => "nr of registrations (active)",
    "nrofregs" => "nr of registrations",
    "nrofrepeatstoschedule" => "nr",
    "nrofstudents" => "# Students",
    "nrofstudentsingroup" => "# Students in group",
    "nroftimes" => "nr of times",
    "numberofstudents" => "nr of students",
    "oddevenweeks" => "odd/even weeks",
    "oddweeks" => "odd weeks",
    "of" => "of",
    "ofwhich" => "of which",
    "ok" => "OK",
    "oldmessage" => "old message",
    "on" => "on",
    "onlyactive" => "only active",
    "onlypastappointments" => "only past appointments found (click slider to see them)",
    "open" => "open",
    "openchecklist" => "open checklist",
    "openfullcalendar" => "open full calendar",
    "openingstudentpage" => "now opening the student page",
    "openlogentry" => "open log enty",
    "openpage" => "open page",
    "openregistration" => "open registration",
    "openstudentfile" => "open student file of :studentName",
    "openstudentgroup" => "open student group",
    "openstudentlistpage" => "open the student list page",
    "openstudentpage" => "open the student page",
    "opentasks" => "open tasks",
    "opentimetable" => "open plan card",
    "opentrialrequests" => "Open trial requests",
    "optional" => "optional",
    "optionalcommaseparatedemailaddresses" => "other, comma separated email addresses",
    "or" => "or",
    "orgstartdatetime" => "original start date/time",
    "orjusttypetekst" => "...and update if neccessary. Or simply directly type the text you want",
    "other" => "other",
    "otherreason" => "other reason",
    "otherweek" => "other week",
    "overdue" => "overdue",
    "overrideprice" => "override price",
    "overridetaxrate" => "override tax rate",
    "overview" => "overview",
    "overviewalarms" => "overview alarms",
    "overviewgroups" => "overview groups",
    "overviewindividualstudents" => "overview individual students",
    "overviewregistrations" => "overview registrations",
    "overviewregistrationschecklists" => "overview registrations with checklist",
    "page" => "page",
    "participating" => "participating",
    "participatingstudents" => "participating students",
    "participation" => "participation",
    "partofday" => "part of day",
    "partofseries" => "part of a series",
    "partofseriesbutmoved" => "used to be part of a series but has been moved",
    "password" => "password",
    "passwordchanged" => "your password has been changed",
    "passwordhasbeenreset" => "the password has been reset to the default password",
    "past" => "past",
    "per" => "per",
    "perday" => "day",
    "permissionautobanktransfer" => "permission automatic banktransfer",
    "permissionsocialshare" => "pemission social share",
    "permonth" => "month",
    "persequence" => "sequence",
    "persistevents" => "persist events",
    "persisteventsandcreatetask" => "persist event and additionally create task for after trial lesson",
    "persisteventsdontcreatetask" => "persist event, dont create task for after trial lesson",
    "personalaccesstokens" => "personal access tokens",
    "pertwoweeks" => "2 weeks",
    "perweek" => "week",
    "pickarecord" => "pick a record",
    "pinnotvalid" => "pin not valid",
    "pinrestrictionsare" => "pin should only contain numeric and alfanumeric characters and should be at least 6 characters in length.",
    "pinsavedcorrectly" => "pin saved correctly",
    "planblocking" => "blocks tutoring planning",
    "plancard" => "planning card",
    "plannedappointments" => "planned appointments",
    "planningbase" => "planning source",
    "planningdetails" => "planning details",
    "planningtable" => "planning table",
    "planningtarget" => "planning taget",
    "plantables" => "planning tables",
    "pleaseaddacoursefirst" => "please add a course first",
    "pleaseaddcontent" => "please add content",
    "pleaseaskadminfordetail" => "please ask your CLASS administrator for details about this email.",
    "pleasecheckyouraddressinfo" => "Please check your address details",
    "pleasechoosea" => "please choose a",
    "pleasechooseadefaultchecklistorcreateanewone" => "Choose a standard checklist to edit or create a new one",
    "pleasechooseanoption" => "please choose an option",
    "pleasechoosearecurrenceoptionorcreateanewone" => "Choose a repeat option to edit or create a new one",
    "pleasechoosetemplate" => "please choose a template, or type a text",
    "pleaseconfirmcreatstudentfomtrial" => "CLASS will now create a student record, based on this trial lesson request. Please confirm",
    "pleaseeditindateexceptionsinterface" => "please edit this event in the <a href=\":url\">dateexceptions interface</a>",
    "pleaseenterlastnameascheck" => "Please enter students lastname (as final check)",
    "pleasemakecorrections" => "please make any corrections to your mail text",
    "pleasemarkyourpreferedtimesegments" => "Please enter your prefered timesements for your lessons",
    "pleasenote" => "please note",
    "pleasenotestudenthasnoaccess" => "please note: the student currently has no access to the public version of this page. ",
    "pleasenotifyusifnotcomplete" => "Is something wrong or are these details incomplete? Let us know at:",
    "pleaserefertomanual" => "Please refer to <a href=\"https://docuclass.scolavisa.eu/doku.php\" target=\"_blank\">the manual</a> for more information on this topic",
    "pleasesavechanges" => "please save your changes",
    "pleasesavefirst" => "please save first",
    "pleaseselectaday" => "please select a day",
    "pleaseselectstudents" => "selected studentnames will be displayed here. Please click a row in the table below to select a studentname.",
    "pleasestartwithactioninlistorcreatenew" => "start with a function in the list or create a new document library",
    "pleaseusesmallimages" => "please use small images (max :maxfilesize each, max :maxtotalfilesize in total). If you want to upload more than one image, you need to select them all at once. Use the CTRL key to select multiple images during uploading.",
    "pleasewait" => "please wait",
    "possiblestudentgroups" => "possible student groups",
    "preconditions" => "preconditions",
    "preferedtime" => "preferedtime",
    "preference" => "preference",
    "preferencesaved" => "preference saved",
    "preferencesince" => "preference filled in at",
    "preferredlanguage" => "preferred language",
    "preferredschedule" => "scheduling preference",
    "preferredtimeforlessonat" => "Your preferred time for your lesson at :schoolname",
    "preferstudentemailthataremarkedfor" => "prefer student email addresses that are marked for",
    "prefsurl" => "preferences url",
    "prepareplanning" => "prepare planning",
    "preposition" => "preposition",
    "present" => "present",
    "previewsignrequest" => "preview sign request",
    "previous" => "previous",
    "previousstudents" => "previous students",
    "previousstudentsonthiscourse" => "Previous students of this course",
    "price" => "price",
    "priceExTax" => "ex tax",
    "priceExTaxSubAdult" => "ex tax < :adultthreshold",
    "priceinvoice" => "inc tax",
    "priceper" => "price is per",
    "priceresetsuccessfull" => "price reset was successfull",
    "pricesavedsuccessfull" => "price successfully saved",
    "printregistrationform" => "print registrationform",
    "processteachingrequests" => "process teaching requests",
    "profile" => "profile",
    "profilephoto" => "profile photo",
    "profileupdatedsuccessfully" => "profile saved",
    "publishevent" => "Publish as event",
    "putonlist" => "put on list",
    "queued" => "queued",
    "quickjump" => "quick-jump",
    "quickjumptocourse" => "quick-jump to another course",
    "quickjumptostudent" => "quick-jump to another student",
    "reactivatealert" => "reactivate this alert",
    "readat" => "read at",
    "readmessage" => "open message",
    "readmessages" => "gelezen berichten",
    "reason" => "reason",
    "receivedlessonrequests" => "received teaching requests",
    "recentlogentries" => "recent log entries",
    "recepient" => "recipient",
    "recipient" => "recipient",
    "recurrenceoptiondata" => "recurrence option data",
    "recurrenceoptions" => "recurrence option|recurrence options",
    "recurrences" => "reccurence|reccurences",
    "regidnotvalid" => "registration id is not valid",
    "registerweblink" => "register weblink",
    "registration" => "registration",
    "registrationdata" => "registration data",
    "registrationdate" => "registrationdate",
    "registrationdeleted" => "registration has been deleted",
    "registrationnotsignedafterrequest" => "not signed after request",
    "registrationnotsignrequested" => "no sign request sent",
    "registrations" => "registrations",
    "registrationsaved" => "course registration saved",
    "registrationsopenchecklist" => "registrations incomplete",
    "regs" => "registration|registrations",
    "remarks" => "remarks",
    "rememberlongisstrong" => "Remember: the longer your password, the stronger it is",
    "removedocfromlib" => "remove document from library",
    "removefromlist" => "remove from list",
    "removefromschedule" => "remove from schedule",
    "removefromthislibrary" => "remove the file from -this- library",
    "removestudentfromgroup" => "remove student from group",
    "repeat" => "repeat",
    "repeatendisbeforeappointmentend" => "repeat-end is earlier then appointment-end",
    "repeatnewpassword" => "repeat new password",
    "repeatnewpasswordisrequired" => "...repeat the new password",
    "repeats" => "repeats",
    "repeatslimitedaccordingtocourse" => "repeats limited according to recurrence setting of the course",
    "repetitions" => "repetition|repetitions",
    "replacedefaulttext" => "replace default text",
    "reportactivestudentsgrouped" => "active students, grouped",
    "reportappointments" => "report appointments",
    "reports" => "reports",
    "reportstudents" => "report students",
    "request" => "request",
    "requestedstartdate" => "requested start date",
    "requestsignature" => "confirm your registration with :customername",
    "requestsignatureemail" => "sign request",
    "resendsignrequest" => "resend sign request",
    "reset" => "reset",
    "resetpassword" => "reset password",
    "restoresavedconcept" => "restore saved concept",
    "retrievingtemplatefailed" => "retrieving chosen template failed",
    "revokeaccess" => "revoke access",
    "revokeaccessfromallstudents" => "revoke access from all students",
    "roomlocation" => "room, location",
    "salutation" => "salutation",
    "salutationforfinancial" => "salutation for financial",
    "salutationforplanning" => "salutation for planning",
    "salutationforpromotion" => "salutation for promotion",
    "save" => "save",
    "saveconcept" => "save concept",
    "saved" => "saved",
    "saveeditschoolyear" => "save school year changes",
    "saveedittask" => "save task changes",
    "savelogentry" => "save log entry",
    "savemandatemumbersuccess" => "saving new mandate number was successful",
    "savenewschoolyear" => "save new school year",
    "savepreference" => "save my preferences",
    "savesuccess" => "saving data was successful",
    "savingfailed" => "saving data was NOT successful!",
    "scheduleassistant" => "schedule assistant",
    "scheduled" => "scheduled",
    "scheduleproposal" => "schedule proposal",
    "schedules" => "schedule|schedules",
    "schedulethreshold" => "nr of days for schedule availability",
    "schoolclosed" => "school closed",
    "schoolcontactperson" => "school contact person",
    "schoollogo" => "school logo",
    "schoolname" => "school name",
    "schoolprivacystatementurl" => "school privacy statement url",
    "schoolratesandconditionsurl" => "school rates and conditions url",
    "schools" => "school|schools",
    "schooltelephone" => "school telephone",
    "schoolwebsite" => "school website",
    "schoolyear" => "school year",
    "schoolyeardata" => "school year data",
    "schoolyeardeleted" => "school year deleted",
    "schoolyearmissing" => "school year missing",
    "schoolyears" => "school year|school years",
    "schoolyearsaved" => "school year saved",
    "search" => "search",
    "searchfile" => "search file",
    "searchfilter" => "search filter",
    "searchfromlist" => "search from list",
    "searchstudent" => "search student",
    "searchstudentbypartofname" => "search the student to copy data from by a minimum of 3 characters of their name in the search box.",
    "seedateexceptions" => "see date exceptions",
    "seefulllog" => "see full log",
    "seetutorschedule" => "see tutor schedule",
    "select" => "select",
    "selectall" => "select all",
    "selectnone" => "select none",
    "selectseries" => "select series",
    "selectstudents" => "select students",
    "send" => "send",
    "sendagain" => "send again",
    "sendallappointmentascalendarfile" => "send all appointments as calendar file",
    "sendappointmentascalendarfile" => "send this appointment as calendar file",
    "sendappointments" => "send these appointments",
    "senddate" => "send date",
    "sendemail" => "send email",
    "sendicsfile" => "send appointment",
    "sendingemailfailed" => "sending email failed",
    "sendmessage" => "send message",
    "sendmessagetoclasse" => "send message<br>to ClassE",
    "sendrequesttoemail" => "send request to email",
    "sendrequesttosalutation" => "use salutation",
    "sendsignrequest" => "send sign request",
    "sent" => "sent",
    "sentemail" => "sent email",
    "sequence" => "sequence",
    "serial" => "serial",
    "setadminastutor" => "set admin account to have tutor rights",
    "setsticky" => "click to pin down: currently this event will not appear in the calendar, because there are other blocking events at the same time",
    "settings" => "settings",
    "settutorrights" => "set as tutor",
    "settutors" => "couple tutors",
    "setupwizard" => "setup wizard",
    "share" => "share",
    "share_with" => "share with",
    "sharedwith" => "shared with",
    "sharedwithcoursegroups" => "shared with :count course group|shared with :count course groups",
    "sharedwithcourses" => "shared with :count course|shared with :count courses",
    "sharedwithstudentgroups" => "shared with :count student group|shared with :count student groups",
    "sharedwithstudents" => "shared with :count student|shared with :count students",
    "sharedwithwholeschool" => "shared with whole school",
    "sharewithcoursegroups" => "share with course groups",
    "sharewithcourses" => "share with courses",
    "sharewithstudentgroups" => "share with studentgroups",
    "sharewithstudents" => "share with students",
    "sharewithwholeschool" => "share with whole school",
    "sharing" => "sharing",
    "show" => "show",
    "showall" => "show all",
    "showalldateexceptions" => "Show all date exceptions (or only conflicts)",
    "showfrom" => "show from",
    "showhiddenalerts" => "hidden alerts",
    "showme" => "show me",
    "showpastappointments" => "show past appointments as well",
    "showrecipients" => "show recipients",
    "showuntil" => "show until",
    "signature" => "signature",
    "signed" => "signed",
    "signedoff" => "signed off",
    "signedoffforcourse" => "signed off for course",
    "signedoffstudents" => "students that signed off for a course in the past",
    "signedon" => "signed on",
    "signrequestbacklog" => "open sign requests",
    "signrequestnotsend" => "sign request NOT sent",
    "signrequestsend" => "sign request sent",
    "signstatus" => "sign status",
    "size" => "size",
    "skippedbecauseoccupied" => "skipped :date because it is occupied. Reason: :reason",
    "skippeddatetimes" => "skipped dates, times",
    "socialmediaprivacyetiquette" => "social media privacy etiquette",
    "solitaryappointment" => "solitary appointment",
    "solutionlink" => "solution link",
    "somestudentsnovalidscheduleprefs" => ":nrofstudents student has no (longer) a valid schedule preference|:nrofstudents students have no (longer) a valid schedule prefference",
    "sorry" => "Sorry",
    "sortby" => "sort by",
    "sortbylist" => "sort by lijst",
    "sortbyname" => "sort by name",
    "sources" => "sources",
    "sourcesandtarget" => "sources and target",
    "specialchars" => "one or more of ' \" / \\ ~ ! @ # \$ % ^ &  * ( ) _ - + = { } [ ] | ; : < > , . ? or 0-9",
    "specificstudentorgroup" => "a specific student or studentgroup",
    "staff" => "staff|staff",
    "start" => "start",
    "startdate" => "start date",
    "startdateoutofrange" => "startdate is out of allowed range",
    "startdatetime" => "start date/time",
    "startingfrom" => "starting from",
    "startplanning" => "start planning",
    "starttime" => "start time",
    "startupload" => "start upload",
    "startyear" => "start year",
    "statementsofagreement" => "statements of agreement",
    "status" => "status",
    "stickyflag" => "sticky flag",
    "stickyflagupdated" => "sticky flag updated",
    "streetline1" => "street and housenumber",
    "streetline2" => "extra street line",
    "student" => "student",
    "studentaccess" => "student access",
    "studentaccesslink" => "Student access link",
    "studentcontactdeleted" => "student contact deleted",
    "studentcontactdeletefailed" => "deletion of student contact failed",
    "studentcreatedfromtrialrequest" => "student created from trial request, forwarding to student card...",
    "studentdatacouldnotberemoved" => "student data could not be removed!",
    "studentdataremoved" => "student data removed",
    "studentdeclinedontime" => "student declined on time",
    "studenteditformtopright" => "see top-right on the student card",
    "studentfile" => "student file",
    "studentfirstname" => "student first name",
    "studentgroupname" => "studentgroup name",
    "studentgroups" => "studentgroup|studentgroups",
    "studentgroupstobepscheduled" => "studentgroups to be planned",
    "studentgroupswanttokeepcurrentschedule" => "studentgroups that want to keep current schedule",
    "studenthasaccess" => "the student currently has access to the public version of this page.",
    "studenthasaccesstoprefspage" => "The student has access to the preferred times page via <a href=':href'>this link</a> (make sure you are not logged in if you want to test this link)",
    "studenthasbeensubscribed" => "The student has been subscribed to the studentgroup",
    "studenthasbeenunsubscribed" => "The student has been unsubscribed from the studentgroup",
    "studenthasnoaccesstoprefspage" => "student does not have access to preference page",
    "studentis" => "the student is :age",
    "studentlistdeleted" => "Student list removed",
    "studentlists" => "studentlist|studentlists",
    "studentname" => "student name",
    "studentnoshow" => "student didn't show up without timely cancelation",
    "studentnumbers" => "student numbers",
    "studentprefs" => "student preferences",
    "students" => "student|students",
    "studentsadded" => "students added",
    "studentsatleastonecourse" => "students with at least one active course",
    "studentsfirstnameis" => "The students firstname is",
    "studentsmissing" => "No students found",
    "studentsnocourses" => "students without an active course",
    "studentsoncourse" => "Students on course",
    "studentsonthiscourse" => "students enlisted on this course",
    "studentsremoved" => "students removed",
    "studentsubscribed" => "student subscribed",
    "studentswithoutvalidprefs" => "students without valid planning preference",
    "studentswithvalidprefs" => "students with valid planning preference",
    "studenttasks" => "tasks concerning this student",
    "studentturns" => "the student turns :age",
    "studentunsubscribed" => "student unsubscribed",
    "studentwoemailaddress" => "student without email address",
    "studentworegistration" => "student without course registration",
    "subject" => "subject",
    "succeeded" => "succeeded",
    "success" => "success",
    "successfullysavednewdatetime" => "successfully saved new date / time",
    "successfullysendappointments" => "appointments sent",
    "suggestion" => "suggestion",
    "sum" => "sum",
    "targetedmailcontacts" => "targeted mail contacts",
    "targetgroupsize" => "target group size",
    "taskclosedby" => "task closed by",
    "taskdata" => "taskdata",
    "taskdeleted" => "task deleted",
    "taskenddate" => "task end date",
    "tasks" => "task|tasks",
    "taskstartdate" => "task start date",
    "tasktype" => "task type",
    "tax" => "tax",
    "taxrate" => "tax rate %",
    "taxrateresetsuccessfull" => "tax rate reset was successfull",
    "taxratesavedsuccessfull" => "tax rate successfully saved",
    "telephone" => "telephone",
    "telephoneextra" => "2nd telephonenumber",
    "telephonenumber" => "telephone number",
    "template" => "template",
    "templates" => "templates",
    "templatetarget" => "template target",
    "templatetext" => "template text",
    "termsandconditions" => "terms and conditions",
    "termsandconditionsapply" => "the terms and conditions of :customerName apply.",
    "testenvironment" => "test environment",
    "testlink" => "test this link",
    "thanksforregistering" => "thank you for registering with",
    "thankyou" => "thank you",
    "thefilewillberenewedineverylibrary" => "the file will be renewed in every library that it is part of",
    "thereareconflicts" => "there are planning conflicts",
    "thereisnochecklist" => "there is no checklist",
    "thisandfutureevent" => "this and future events",
    "thiscannotbeundone" => "this action cannot be undone",
    "thischangeneedstobesaved" => "this change needs to be saved",
    "thiscourseplanning" => "planning for this course",
    "thisevent" => "this event",
    "thisfieldismandatory" => "this field is mandatory",
    "thisinformationisinavailableouradministration" => "The following information is known to us and will be used for scheduling the classes.",
    "thisisatrialrequeststudent" => "this is a trial lesson request student. It has been automatically created by CLASS.",
    "thismonth" => "this month",
    "thissubscriptionhasactiveplanning" => "this subscription has an active planning",
    "thissubscriptionhasnoactiveplanning" => "this subscription has no active planning",
    "time" => "time",
    "timeisalways235959" => "time is always 23:59:59, the whole day",
    "timeisempty" => ":field time is empty",
    "timeisnotvalid" => ":field time is not valid",
    "timeistooearly" => ":field time is too early",
    "timeonce" => "time (once)",
    "times" => "times",
    "timesegment" => "time segment",
    "timesegmentnotontutorsschedule" => "Time segment is not (fully) within the teachers default schedule",
    "timesegmentnotontutorsscheduleday" => "Time segment is not (fully) within the teachers default schedule (wrong day)",
    "timesegmentnotontutorsscheduletime" => "Time segment is not (fully) within the teachers default schedule (wrong time)",
    "timespan" => "timespan",
    "timetabledata" => "time table data",
    "timetables" => "time table|timetables",
    "timeunit" => "time unit",
    "title" => "title",
    "to" => "to",
    "tobeplanned" => "to be planned",
    "tobescheduled" => "to be scheduled",
    "toggleall" => "toggle all on/off",
    "toggletoshowallevents" => "toggle to show all events",
    "toggletoshowonlyfutureevents" => "toggle to show only future events",
    "tools" => "tools",
    "toomanyrepeatsfordateexception" => "too many repeats, passed max for date exceptions",
    "tostartorvieplanning" => "to start planning or view the planning",
    "totalnrofcourses" => "total number of courses",
    "totalnrofstudents" => "total number of students",
    "totalnrofstudentsonthislist" => "total number of students on this list",
    "totals" => "total|totals",
    "totalsinthisscreen" => "totals in this screen",
    "totaltimetoolow" => "hour. That's very little to plan with. We hope you can fill in some extra options!",
    "totimeshouldbegreaterthanfromtime" => "the to-time should be greater than the from-time",
    "trialalreadyhascoupledstudent" => "trial lesson request already has a coupled student",
    "triallesson" => "trial lesson",
    "triallessonrequests" => "trial lesson request|trial lesson requests",
    "triallessons" => "trial lessons",
    "triallimitcoursesreached" => "trial limit reached: courses",
    "triallimitstudentsreached" => "trial limit reached: students",
    "triallimittutorsreached" => "trial limit reached: tutors",
    "trialoptionmissing" => "trial option is missing",
    "trialrequest" => "trial lesson request",
    "trialrequestdata" => "trial lesson request data",
    "trialrequests" => "trial lesson request|trial lesson requests",
    "tryagainignorconflicts" => "try again, ignoring any tutor conflicts and location conflicts",
    "tryingfallbacklocation" => "Location is occupied. CLASS is trying the fallback location",
    "tryingtofindnewdatetime" => "CLASS is trying to find a new date/time",
    "tutoralreadyteaching" => "the tutor is already teaching at that moment",
    "tutordata" => "tutor information",
    "tutordeleted" => "tutor deleted",
    "tutoredin" => "tutored in",
    "tutoringevents" => "tutoring events",
    "tutornotactive" => "tutor has no active account",
    "tutornotavailable" => "tutor not available",
    "tutornotavailableaccordingtoschedule" => "tutor not available according to their working schedule",
    "tutornotavailablehasevent" => "tutor not available because of an existing tutoring event",
    "tutors" => "tutor|tutors",
    "tutorsmissing" => "no tutors found",
    "twofactorauthenticate" => "authenticate",
    "twofactorauthentication" => "2 factor security",
    "twofactorauthenticatorcode" => "Authenticator Code",
    "twofactorconfirm" => "confirm enable 2 factor security",
    "twofactordisable" => "Disable 2 factor security",
    "twofactordisableexplain" => "If you wish to disable 2 factor security: please enter your password and click [Disable 2 factor security] button below.",
    "twofactorenabledsuccess" => "2 factor security has been enabled successfully.",
    "twofactorexplain" => "2 factor security (2FA) strengthens access security by requiring two methods (also referred to as factors) to verify your identity: your password and a one time generated code. Two factor authentication protects against phishing, social engineering and password brute force attacks and secures your logins from attackers exploiting weak or stolen credentials.",
    "twofactorgeneratekey" => "generate a secret key to enable 2 factor security",
    "twofactorinvalidcode" => "Invalid verification code, Please try again.",
    "twofactorisenabled" => "2 factor security is currently <strong>enabled</strong> on your account.",
    "twofactorismissing" => "2 factor security has not been activated yet.",
    "twofactornowdisabled" => "2 factor security has been deactivated",
    "twofactorsecretkeygenerated" => "Your code has been generated.",
    "twofactorstep1" => "Scan this QR code with your authenticator app. Alternatively, you can use the code:",
    "twofactorstep2" => "Enter the pin from authenticator app",
    "twoweeks" => "two weeks",
    "type" => "type",
    "typeDesc" => "type / description",
    "typetosearch" => "type to search",
    "unabletodeletecourse" => "unable to delete this course",
    "unabletodeletetutor" => "unable to delete this tutor",
    "uncheckeditems" => "unchecked items",
    "uniquestudentidentifier" => "unique studentidentifier",
    "unknown" => "unknown",
    "unlink" => "unlink",
    "unreadmessages" => "unread messages",
    "unregister" => "unsubscribe",
    "unregisterdate" => "unsubscribe date",
    "unregistered" => "unsubscribed",
    "unsetsticky" => "click to unpin: currently this event will be in the calendar, even though there are other, blocking events at the same time",
    "until" => "until",
    "until_unregister_indefinitely" => "until unsubscribe (ongoing)",
    "untilincluding" => "until, including",
    "updated" => "updated",
    "updatefailed" => "update failed",
    "updatefile" => "upload a new version in -all- libraries",
    "updatemydatainclass" => "please update my student information in Class",
    "updatesuccess" => "update successful",
    "upload" => "upload",
    "uploaddocandlink" => "upload a new document and link it to this library",
    "uploadICS" => "upload ICS",
    "uploadimagesyouwillbeusing" => "Upload images you will be using in your email here.",
    "uploading" => "uploading",
    "uploadingandsaving" => "uploading and saving....",
    "uploadnewversionoffile" => "upload a new version of this file",
    "use&&tosearchformoreargs" => "use && to search for more than one argument",
    "usefor" => "use for",
    "useforfieldupdated" => "&quot;use-for&quot; field updated",
    "useforfinance" => "financial",
    "useforplanning" => "planning",
    "useforpromotions" => "promotions",
    "usesalutation" => "alternative salutation",
    "validating" => "checking for conflicts",
    "value" => "value",
    "variables" => "variable|variables",
    "variant" => "variant",
    "variantcode" => "variant code",
    "vatliable" => "vat liable",
    "view" => "view",
    "viewavatar" => "View avatar",
    "viewsignrequest" => "view sign request",
    "viewstudentspreferencepage" => "go to this student's preferences page",
    "wantstokeepdatetime" => "wants to keep this day/time",
    "warn" => "warning",
    "warnbeforeadultstudents" => "warn vat liable student (days)",
    "warnbeforebirthdaystudents" => "warn birthday students (days)",
    "warndeletetoken" => "Are you sure you wish to delete this token? This cannot be undone. You may loose access from your website to CLASS!",
    "warnsendmailtoselffirst" => "please send a test email to yourself first to test your mail layout",
    "warnunsavedchanges" => "If you leave this page you will lose your unsaved changes.",
    "webdescription" => "webdescription (Wordpress)",
    "weburlofyourprofilephoto" => "web url of your profile photo",
    "week" => "week",
    "weeks" => "week|weeks",
    "welcome" => "welcome",
    "wewillbedeleting" => "we will be deleting this",
    "whichplanningasstartingpoint" => "which planning forms the basis for this planning session?",
    "whichplanningastarget" => "in which schedule do you want to include the new appointments?",
    "whichregistrationstoplan" => "which registration do you wish to schedule?",
    "who" => "who",
    "wholeschool" => "whole school",
    "willbe" => "will be",
    "with" => "with",
    "withoutplanningpreferrence" => "without planning preferrence",
    "withoutpreviousplanning" => "without previous planning",
    "withplanningpreferrence" => "with planning preferrence",
    "withpreviousplanning" => "with previous planning",
    "wordpresscode" => "WordPress code",
    "working" => "Working",
    "wouldyouliketocreategroup" => "Would you like to create a studentgroup?",
    "wrongtargetcourse" => "This student is registered on a different course than is handled by this group",
    "year" => "year",
    "years" => "year|years",
    "yes" => "yes",
    "yessend" => "yes, send",
    "youareabouttodeletetimeslice" => "you are about to delete this timeslice",
    "youareusingdefaultpw" => "you are using the default password. You need to change this before you get access to Class.",
    "youcanaddrecurrenceoptionshere" => "you can add recurrenceoptions <a href=\"/recurrenceoptions\">here</a>",
    "youcanfindthemhere" => "you can find them here: :tanccUrl",
    "youcannowclosethiswindow" => "you can now close this window",
    "youcantthisbecauseisdateexception" => "you can't change this entry here because it is a date exception.",
    "youcanusetheseplaceholders" => "You can use placeholders in your text. You will find them in the menu under [placeholders]. They will be replaced automatically in the actual email.",
    "youhavenocourses" => "you have no active registration for a course (yet)",
    "youhavenotcreatedtokens" => "you have not created any access tokens (yet)",
    "youhavesuccessfullyuploadedimages" => "You have successfully uploaded an image.|You have successfully uploaded :nrofimages images.",
    "youneedthisidtodelete" => "you need this number to remove all data for the student (see your profile page)",
    "yourappointmentwith" => "your appointment with :customername",
    "youravailability" => "when are you available?",
    "yourfirstupcomingappointment" => "Your first upcoming appointment",
    "yournewpassword" => "your new password",
    "yournewpasswordis" => "your new password is",
    "yournewpasswordneedsto" => "status password change",
    "yourregistrationisnowconfirmed" => "your registration is now confirmed",
    "zipcode" => "zipcode"
];
