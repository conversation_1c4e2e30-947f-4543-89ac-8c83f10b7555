<template>
    <panel :busy="busy">
        <template v-slot:title>
            {{ ucFirst(translate('generic.emailsettings')) }}
            <small>
                {{ ucFirst(translate('generic.explainaddressfields')) }}
            </small>
        </template>
        <template v-if="!busy && domainData?.domainName">
            <!-- ROW 2            -->
            <div class="d-flex flex-wrap">
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- name contact person -->
                    <label for="name_contact_person">{{ ucFirst(translate('generic.namecontactperson')) }}</label>
                    <input class="form-control"
                           name="name_contact_person"
                           id="name_contact_person"
                           type="text"
                           :value="domainData.strAddress.contactPersonName"
                    />
                </div>
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- Logo url -->
                    <label for="logo_url">{{ ucFirst(translate('generic.logourl')) }}</label>
                    <input class="form-control"
                           name="logo_url"
                           id="logo_url"
                           type="text"
                           v-model="domainData.logoUrl"
                    />
                </div>
                <div class="flex-grow-1 form-group">
                    <!-- preview logo image -->
                    <div class="parent-container">
                        <img v-if="domainData.logoUrl" :src="domainData.logoUrl" height="60px" alt="logo url"/>
                        <span v-else class="image-unknown">?</span>
                    </div>
                </div>
                <div class="flex-grow-1 form-group">
                    <!-- spare section-->
                </div>
            </div>
            <!-- ROW 3            -->
            <div class="d-flex flex-wrap">
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- address line 1 -->
                    <label for="address_line_1">{{ ucFirst(translate('generic.addressline1')) }}</label>
                    <input class="form-control"
                           name="address_line_1"
                           id="address_line_1"
                           type="text"
                           v-model="domainData.strAddress.address1"

                           required
                    />
                </div>
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- address line 2 -->
                    <label for="address_line_2">{{ ucFirst(translate('generic.addressline2')) }}</label>
                    <input class="form-control"
                           name="address_line_2"
                           id="address_line_2"
                           type="text"
                           v-model="domainData.strAddress.address2"
                    />
                </div>
            </div>
            <!-- ROW 4            -->
            <div class="d-flex flex-wrap">
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- zipcode -->
                    <label for="zipcode">{{ ucFirst(translate('generic.zipcode')) }}</label>
                    <input class="form-control"
                           name="zipcode"
                           id="zipcode"
                           type="text"
                           v-model="domainData.strAddress.zip"
                           required
                    />
                </div>
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- city -->
                    <label for="city">{{ ucFirst(translate('generic.city')) }}</label>
                    <input class="form-control"
                           name="city"
                           id="city"
                           type="text"
                           v-model="domainData.strAddress.city"
                           required
                    />
                </div>
                <div class="flex-grow-1 mr-2 form-group">
                    <!-- Telephone -->
                    <label for="telephone">{{ ucFirst(translate('generic.telephone')) }}</label>
                    <input class="form-control"
                           name="telephone"
                           id="telephone"
                           type="text"
                           v-model="domainData.strAddress.telephone"
                    />
                </div>
                <div class="flex-grow-1 form-group">
                    <!-- email -->
                    <label for="email">{{ ucFirst(translate('generic.email')) }}</label>
                    <input class="form-control"
                           name="email"
                           id="email"
                           type="text"
                           v-model="domainData.strAddress.email"
                           required
                    />
                </div>

            </div>
        </template>
    </panel>
</template>

<script setup>
import useDomain from "../../composables/useDomain";
import useLang from "../../composables/useLang";
import Panel from "../Layout/Panel.vue";

const { ucFirst, translate } = useLang();
const { busy, domainData } = useDomain();
</script>

<style scoped>
.image-unknown {
    display: inline-block;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background-color: #f0f0f0;
    color: #666;
    font-size: 24px;
    align-self: flex-start;
}

.parent-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    height: 100%;
}

.parent-container img {
    height: 60px;
    object-fit: contain; /* Ensures the image retains its aspect ratio */
    align-self: flex-start;
}
</style>
