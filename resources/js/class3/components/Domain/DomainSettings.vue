<template>
    <div>
        <GenericSettings />
        <EmailSettings />
        <floating-bar>
            <button class="btn btn-success mr-2" @click="cancel">{{ ucFirst(translate('generic.cancel')) }}</button>
            <button class="btn btn-primary" @click="save">{{ ucFirst(translate('generic.save')) }}</button>
        </floating-bar>
    </div>
</template>

<script setup>
import { onMounted } from "vue";
import GenericSettings from "./GenericSettings.vue";
import EmailSettings from "./EmailSettings.vue";
import FloatingBar from "../Layout/FloatingBar.vue";
import useDomain from "../../composables/useDomain";
import useLang from "../../composables/useLang";
import useNoty from "../../composables/useNoty";


const { ucFirst, translate } = useLang();
const { getDomainData, updateDomainData } = useDomain();
const { successNoty, failNoty } = useNoty();

onMounted(async () => {
    await getDomainData();
    addAsteriskOnRequiredFields();
});

const save = async () => {
    try {
        await updateDomainData();
        successNoty(translate('generic.saved'), translate('generic.success'), 5000);
        await getDomainData();
        addAsteriskOnRequiredFields();
    } catch (e) {
        failNoty(e, translate('generic.error'));
    }
};

const cancel = () => {
    window.location.href = '/';
};

const addAsteriskOnRequiredFields = () => {
    const requiredFields = document.querySelectorAll('input[required]');
    requiredFields.forEach((field) => {
        // find the label for the input field
        const label = document.querySelector(`label[for="${field.id}"]`);
        // add an asterisk after the label
        label.insertAdjacentHTML('afterend', '<span class="text-danger ml-1"><strong>*</strong></span>');
    });
};

</script>

<style scoped>

</style>
