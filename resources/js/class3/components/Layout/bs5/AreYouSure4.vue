<template>
    <modal
        :popup-title = "ucFirst(translate('generic.areyousure'))"
        :closetext   = "ucFirst(translate('generic.cancel'))"
        :modal-id     = "modalId"
    >
        <p v-if="confirmText !== ''" v-html="confirmText"></p>
        <p v-else>
            {{ucFirst(translate('generic.thiscannotbeundone'))}}
        </p>
        <slot></slot>
        <template v-slot:okbutton>
            <button
                type="button"
                class="btn btn-danger"
                data-bs-dismiss="modal"
                @click.prevent="confirmClicked"
                data-testid="confirm-delete-button"
            >
                {{ buttonText === '' ? ucFirst(translate('generic.delete')) : buttonText }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import Modal from './Modal4';
import useLang from "../../../composables/useLang";

const { translate, translateChoice, ucFirst } = useLang();

const props = defineProps({
    buttonText: {
        type: String,
        default: ''
    },
    modalId: {
        type: String,
        required: true
    },
    confirmText: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['confirmclicked']);

const confirmClicked = () => {
    emit('confirmclicked');
};
</script>

<style scoped>

</style>
