<template>
    <div :class="'card shadow my-2 ' + extraClass" :data-testid="panelTestId">
        <!-- Card Header -->
        <div
            class="card-header py-3 d-flex flex-row align-items-center justify-content-between"
            aria-expanded="true"
        >
            <h6 class="m-0 font-weight-bold text-primary">
                <slot name="title">Please add a title</slot>
            </h6>
            <span class="float-end">
                <slot name="subtitle" class="me-1"></slot>
                <span
                    v-if="collapsible"
                    class="btn btn-primary btn-sm ms-2 px-3 collapse-button"
                    @click.prevent="triggerCollapse"
                    :aria-expanded="!isCollapsed"
                    :aria-controls="uid"
                >
                    <!-- can't use fontawesome reactive for some reason -->
                    <span v-if="isCollapsed" class="iconfont"> &#8711; </span>
                    <span v-else class="iconfont"> &#8710; </span>
                </span>
            </span>
        </div>
        <!-- Card Body - extra wrapper div to prevent stutter when collapsing -->
        <div :id="uid" :class="['collapse', { show: !isCollapsed }]" :aria-expanded="!isCollapsed">
            <div class="card-body">
                <spinner-svg v-if="busy" size="50px"/>
                <slot v-else>Please add some body content</slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import SpinnerSvg from './SpinnerSvgBs5';
import useUtils from "../../../composables/useUtils";

const { uniqueId } = useUtils();

const props = defineProps({
    busy: {
        type: Boolean,
        default: false
    },
    collapsible: {
        type: Boolean,
        default: false
    },
    collapse: {
        type: Boolean,
        default: false
    },
    extraClass: {
        type: String,
        default: ''
    },
    panelTestId: {
        type: String,
        default: 'panel'
    }
});

const uid = uniqueId();
const isCollapsed = ref(false);

const triggerCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
};

onMounted(() => {
    // start collapsed if collapse is true
    if (props.collapse) {
        isCollapsed.value = props.collapse;
    }
});
</script>

<style scoped lang="scss">
.card-header .form-check {
    margin-bottom: 0;
}

.point {
    cursor: pointer;
}

.collapse-button {
    &:hover {
        background-color: var(--bs-primary);
        color: white !important;
    }
    
    &.iconfont {
        width: 3rem;
        font-size: 1rem;
        color: var(--bs-primary);

        &:hover {
            color: white;
        }
    }
}
</style>
