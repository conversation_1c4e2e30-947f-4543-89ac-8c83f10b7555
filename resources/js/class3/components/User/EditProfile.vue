<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.editprofile')) }}</h3>
        </template>
        <div class="row">
            <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <profile-entry />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <change-password />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <delete-student />
                    </div>
                </div>
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useLang from "../../composables/useLang";
import useProfile from "../../composables/useProfile";
import useNoty from "../../composables/useNoty";
import Panel from "../Layout/Panel.vue";
import ProfileEntry from "./ProfileEntry.vue";
import ChangePassword from "./ChangePassword.vue";
import DeleteStudent from "./DeleteStudent.vue";

const { ucFirst, translate } = useLang();
const { busy, getProfile } = useProfile();
const { failNoty } = useNoty();

onMounted(async () => {
    try {
        await getProfile();
    } catch (error) {
        failNoty(error.message);    
    }
});
</script>

<style scoped>

</style>
