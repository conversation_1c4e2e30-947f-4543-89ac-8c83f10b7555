<template>
    <div>
        <strong>{{ ucFirst(translate('generic.preferredlanguage')) }}</strong>
        <div class="row" v-if="profile">
            <div class="col-xs-12 col-xl-12">   
                <label v-tooltip="translate('generic.dutch')">
                    <input type="radio" v-model="profile.preferred_language" value="nl" />
                    <img src="/images/nl.svg" class="lang-img" />
                </label>
                <label v-tooltip="translate('generic.english')">
                    <input type="radio" v-model="profile.preferred_language" value="en" />
                    <img src="/images/gb.svg" class="lang-img" />
                </label>
            </div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';
import useLang from '../../composables/useLang';
import useProfile from '../../composables/useProfile';

const { ucFirst, translate } = useLang();
const { profile } = useProfile();

// Set language after profile is loaded
watch(profile, (newProfile) => {
    if (newProfile) {
        newProfile.preferred_language = newProfile.preferred_language || newProfile.domain?.language;
    }
}, { immediate: true });
</script>
