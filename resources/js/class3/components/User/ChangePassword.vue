<template>
    <div>
        <strong>{{ ucFirst(translate('generic.password')) }}</strong>
        <div class="row">
            <div class="col-xs-12 col-xl-12">
                <button class="btn btn-danger" data-toggle="modal" data-target="#changePassword">
                    {{ucFirst(translate('generic.changepassword'))}}
                </button>
            </div>
        </div>
        <Modal 
            modal-id="changePassword" 
            :popup-title="ucFirst(translate('generic.changepassword'))" 
            :closetext="ucFirst(translate('generic.close'))" 
        >
            <EnterPasswordChange />
            <template #okbutton>
                <button 
                    class="btn btn-danger" 
                    @click="changePassword" 
                    :disabled="passwordErrors.length > 0" 
                    data-dismiss="modal"
                >
                    {{ucFirst(translate('generic.changepassword'))}}
                </button>
            </template>
        </Modal>
    </div>  
</template>

<script setup>
import useLang from '../../composables/useLang';
import usePassword from '../../composables/usePassword';
import Modal from '../../components/Layout/Modal3.vue';
import EnterPasswordChange from './EnterPasswordChange.vue';
const { ucFirst, translate } = useLang();
const { changePassword, passwordErrors } = usePassword();
</script>   
