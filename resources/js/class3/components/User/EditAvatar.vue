<template>
    <div>
        <div class="row" v-if="profile">
            <div class="col-xs-12 col-xl-12">
                <div class="row">
                    <div class="col-xs-7     col-xl-7">
                        <div class="form-group">
                            <img v-if="profile.avatar" :src="profile.avatar" height="100px"/>
                            <img v-else src="/images/no-profile.png" height="100px"/>
                        </div>
                    </div>
                    <div class="col-xs-5 col-xl-5">
                        <language-switch />
                    </div>
                </div>
            </div>
        </div>
        <div class="row" v-if="profile">
            <div class="col-xs-12 col-xl-12 mt-2">
                <div class="form-group">
                    <label for="avatar">{{ ucFirst(translate('generic.weburlofyourprofilephoto')) }}</label>
                    <input class="form-control" placeholder="Avatar URL" v-model="profile.avatar" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import useLang from '../../composables/useLang';
import useProfile from '../../composables/useProfile';
import LanguageSwitch from './LanguageSwitch.vue';

const { ucFirst, translate } = useLang();
const { profile } = useProfile();
</script>