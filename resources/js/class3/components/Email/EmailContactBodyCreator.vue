<template>
    <panel>
        <template #title>
            <i class="fas fa-envelope"></i>
            {{ ucFirst(translate("generic.mailbody")) }}
            <br/>
            <small>
                {{ ucFirst(translate("generic.mailto")) }}:
                <span
                    v-if="cbStudents"
                    class="badge badge-pill badge-primary"
                    data-toggle="popover"
                    :data-content="studentEmailAddressesAsString"
                    title="Student email addresses"
                >
                  {{ ucFirst(translateChoice("generic.students", 2)) }}:
                  {{ nrOfStudentEmails }}
                </span>
                <span
                    v-if="cbStaff"
                    class="badge badge-pill badge-primary"
                    data-toggle="popover"
                    :data-content="staffEmailAddressesAsString"
                    title="Staff email addresses"
                >
                  {{ ucFirst(translateChoice("generic.staff", 2)) }}:
                  {{ nrOfStaffEmails }}
                </span>
                <span
                    v-if="extraEmailAddresses.length > 0"
                    class="badge badge-pill badge-primary"
                    data-toggle="popover"
                    :data-content="extraEmailAddresses.join(', ')"
                    title="Extra email addresses"
                >
                  {{ translate("generic.extraemailaddresses") }}:
                  {{ extraEmailAddresses.length }}
                </span>
                <div
                    v-if="cbStudents || cbStaff"
                    v-tooltip="translate('generic.warnsendmailtoselffirst')"
                    class="badge badge-danger badge-pill"
                >
                    <i class="fa fa-exclamation-circle"/>
                </div>
            </small>
        </template>
        <div class="row">
            <div class="col-8">
                <input
                    type="text"
                    class="form-control"
                    v-model="mailSubject"
                    :placeholder="ucFirst(translate('generic.subject'))"
                />
                <hr/>
<!--                <ckeditor-->
<!--                    :editor="editor"-->
<!--                    v-model="mailBody"-->
<!--                    :config="editorConfigWithMediaAndImage"-->
<!--                />-->
            </div>
            <div class="col-4">
                <button
                    class="btn btn-primary"
                    :disabled="!sendable"
                    data-toggle="modal"
                    data-target="#areYouSureSendModal"
                >
                    <i class="fas fa-arrow-right"></i>
                    {{ ucFirst(translate("generic.sendemail")) }}
                </button>
                <hr/>
                <button class="btn btn-primary" @click="saveConceptToLocalStorage">
                    <i class="fas fa-save"></i>
                    {{ ucFirst(translate("generic.saveconcept")) }}
                </button>
                <span v-tooltip="ucFirst(translate('generic.explainonlyoneconcept'))">
                  <i class="fas fa-info-circle"></i>
                </span>

                <div class="mt-4">
                    <button
                        v-if="hasMailConceptInLocalStorage"
                        class="btn btn-outline-success btn-sm"
                        @click="getConceptFromLocalStorage"
                    >
                        {{ ucFirst(translate("generic.restoresavedconcept")) }}
                    </button>
                    <button
                        v-if="hasMailConceptInLocalStorage"
                        class="btn btn-outline-danger btn-sm"
                        data-toggle="modal"
                        data-target="#areYouSureClearLSModal"
                    >
                        {{ ucFirst(translate("generic.deletesavedconcept")) }}
                    </button>
                </div>
                <hr/>
                <label>
                    {{ ucFirst(translate("generic.uploadimagesyouwillbeusing")) }}
                    <span
                        v-tooltip="
                          ucFirst(
                            translate('generic.pleaseusesmallimages', {
                              maxfilesize: convertBytesToHumanReadable(maxImageSize),
                              maxtotalfilesize:
                                convertBytesToHumanReadable(maxTotalImagesSize),
                            })
                          )
                        "
                    >
                        <i class="fas fa-info-circle"/>
                    </span>
                </label>
                <br/>
                <input type="file" @change="previewFiles" multiple/>
                <!-- info after uploading, info will be here (has preference) -->
                <div v-if="uploadedImagesResponse">
                    <ul class="thumbnail-list">
                        <li v-for="(image, index) in uploadedImagesResponse.images" :key="index">
                            <img :src="uploadedImagesResponse.baseUrlToImages + '/' + image.name_in_url"
                                 class="thumbnail"
                                 v-tooltip="image.original_name + ' (' + convertBytesToHumanReadable(image.size) + ')'"
                            />
                            <button
                                v-tooltip="translate('generic.explainimageusageinemail')"
                                @click="insertImageUrl(uploadedImagesResponse.baseUrlToImages + '/' + image.name_in_url)"
                            >
                                <i class="fa fa-copy"></i>
                            </button>
                        </li>
                    </ul>
                </div>
                <!-- info during uploading -->
                <div v-else-if="imagesToUpload.length > 0">
                    <strong v-html="uploadMessage"/>
                    <ul>
                        <li v-for="(image, index) in imagesToUpload" :key="index">
                            {{ image.name }} ({{ image.size }} bytes)
                        </li>
                    </ul>
                </div>
                <hr/>
                <em>{{ ucFirst(translate("generic.explaindefaultsignaturewillbeadded")) }}</em>
            </div>
        </div>

        <are-you-sure
            modal-id="areYouSureSendModal"
            :button-text="ucFirst(translate('generic.yessend'))"
            :confirm-text="ucFirst(translate('generic.areyousuresendemail'))"
            @confirmclicked="sendEmail"
        />
        <are-you-sure
            modal-id="areYouSureClearLSModal"
            @confirmclicked="removeConceptFromLocalStorage"
        />
    </panel>
</template>

<script setup>
import Panel from '../Layout/Panel.vue';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import CKEditor from '@ckeditor/ckeditor5-vue';
import { onUpdated } from 'vue';
import AreYouSure from '../Layout/AreYouSure.vue';
import $ from 'jquery';
import useLang from "../../composables/useLang";
import useConversion from '../../composables/useConversion';
import useEmailContacts from '../../composables/useEmailContacts';
import useConfigItems from '../../composables/useConfigItems';

const { editorConfigWithMediaAndImage } = useConfigItems();
// const editor = ClassicEditor;
// const ckeditor = CKEditor.component;
const { convertBytesToHumanReadable } = useConversion();
const { translate, translateChoice, ucFirst } = useLang();

const {
    cbStaff,
    cbStudents,
    extraEmailAddresses,
    getConceptFromLocalStorage,
    hasMailConceptInLocalStorage,
    insertImageUrl,
    imagesToUpload,
    mailBody,
    mailSubject,
    maxImageSize,
    maxTotalImagesSize,
    nrOfStaffEmails,
    nrOfStudentEmails,
    previewFiles,
    removeConceptFromLocalStorage,
    saveConceptToLocalStorage,
    sendable,
    sendEmail,
    staffEmailAddressesAsString,
    studentEmailAddressesAsString,
    uploadedImagesResponse,
    uploadMessage
} = useEmailContacts();

onUpdated(() => {
    $('[data-toggle="popover"]').popover({
        html: true,
        trigger: 'hover',
        placement: 'bottom'
    });
});
</script>

<style scoped>
/* Max Width of the popover depending on the container! */
.popover {
    max-width: 70%;
}

.thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border: solid 1px #ccc;
}

ul.thumbnail-list {
    margin-top: 1rem;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    padding: 0;
}
</style>
