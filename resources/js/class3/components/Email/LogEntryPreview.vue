<template>
  <modal
    :popup-title="popupTitle"
    :closetext="ucFirst(translate('generic.close'))"
    :modal-id="modalId"
    size="large"
  >
    <!-- status of email (meta) -->
    <div class="row">
      <div class="col-2">
        <strong>{{ ucFirst(translate("generic.status")) }}</strong>
      </div>
      <div class="col-4">
        {{ ucFirst(translate("generic." + emailLog.status)) }}
      </div>
    </div>
    <div class="row">
      <div class="col-2">
        <strong>{{ ucFirst(translate("generic.created")) }}</strong>
      </div>
      <div class="col-4">{{ displayDateTime(emailLog.created_at, true) }}</div>
      <div class="col-2">
        <strong>{{ ucFirst(translate("generic.updated")) }}</strong>
      </div>
      <div class="col-4">{{ displayDateTime(emailLog.updated_at, true) }}</div>
    </div>
    <div class="row">
      <div class="col-2">
        <strong>{{ ucFirst(translateChoice("generic.messages", 1)) }}</strong>
      </div>
      <div class="col-10" v-html="emailLog.log"></div>
    </div>
    <hr />
    <!-- body of email -->
    <!-- the iframe contains the styling inside the emailLog.body -->
    <!-- so it doesn't influence the parent styling -->
    <iframe :srcdoc="emailLog.body" width="100%" height="100%"></iframe>
  </modal>
</template>

<script setup>
import { computed } from 'vue';
import Modal from '../Layout/Modal3.vue';
import useDateTime from '../../composables/useDateTime';
import useLang from "../../composables/useLang";

const { displayDateTime } = useDateTime();
const { translate, translateChoice, ucFirst } = useLang();

const props = defineProps({
    emailLog: {
        type: Object,
        required: true
    },
    modalId: {
        type: String,
        required: true
    }
});
const popupTitle = computed(() => {
    return props.emailLog?.subject;
});
</script>

<style scoped></style>
