<template>
    <Panel4 :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translateChoice('generic.recurrenceoptions', 2)) }}</h3>
        </template>
        <template #subtitle>
            <button
                class="btn btn-primary"
                @click.prevent="recurrenceOptionToEdit = emptyRecurrenceOption"
            >
                {{ ucFirst(translate('generic.newrecurrenceoption')) }}
            </button>
        </template>
        <div class="row">
            <div class="col-xs-12 col-xl-5 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <ListRecurrenceOptions />
                    </div>
                </div>
            </div>
            <div class="col-xs-12 col-xl-7 d-xl-flex flex-xl-column">
                <div class="card h-100">
                    <div class="card-body">
                        <EditRecurrenceOption />
                    </div>
                </div>
            </div>
        </div>
    </Panel4>
</template>

<script setup>
import { onMounted } from "vue";
import Panel4 from "../Layout/bs5/Panel4.vue";
import useLang from "../../composables/useLang";
import useRecurrenceOptions from "../../composables/useRecurrenceOptions";
import ListRecurrenceOptions from "./ListRecurrenceOptions.vue";
import EditRecurrenceOption from "./EditRecurrenceOption.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { busy, emptyRecurrenceOption, getRecurrenceOptions, recurrenceOptionToEdit } = useRecurrenceOptions();

onMounted(() => {
    getRecurrenceOptions();
});

</script>

<style scoped>

</style>
