<template>
<div>
    <div class="d-flex flex-row mb-3">
        <!-- flex: bootstrap 4 -->
        <div class="mr-3" style="flex: 2">
            <CourseGeneric
                :course-groups="filteredCourseGroups"
                :recurrence-options="filteredRecurrenceOptions"
                :domain="domain"
            />
        </div>
        <div style="flex: 1" v-if="courseId > 0">
            <CourseScheduling></CourseScheduling>
        </div>
    </div>
    <!-- only in edit mode -->
    <template v-if="courseId > 0">
        <StudentRegistrationList filter="active_registration"></StudentRegistrationList>
        <StudentRegistrationList filter="no_active_registration"></StudentRegistrationList>
        <CourseStudentGroups></CourseStudentGroups>
    </template>
    <floating-bar>
        <a href='/home' class="btn btn-secondary">{{ucFirst(translate('generic.dashboard'))}}</a>
        <a href='/courses' class="btn btn-secondary">{{ucFirst(translate('generic.list'))}}</a>
        <button class="btn btn-primary" @click="saveCourseData" :disabled="!isSaveable">{{ ucFirst(translate('generic.save')) }}</button>
    </floating-bar>
</div>
</template>

<script setup>
import { computed, onMounted } from "vue";
import useBaseData from "../../composables/useBaseData";
import useLang from "../../composables/useLang";
import useCourse from "../../composables/useCourse";

import CourseGeneric from "./CourseGeneric.vue";
import StudentRegistrationList from "./StudentRegistrationList.vue";
import CourseScheduling from "./CourseScheduling.vue";
import CourseStudentGroups from "./CourseStudentGroups.vue";
import FloatingBar from "../Layout/FloatingBar.vue";

const { ucFirst, translate } = useLang();
const { courseId, isSaveable, initCourseData, saveCourseData } = useCourse();

const props = defineProps({
    courseId: {
        type: Number,
        default: 0
    },
});

const { allCourseGroups, allRecurrenceOptions, domain, initBaseData } = useBaseData();
const filteredCourseGroups = computed(() => {
    // we only need the course groups, not all related data
    return allCourseGroups.value.map(cg => {
        return {
            id: cg.id,
            name: cg.name,
        };
    });
});

const filteredRecurrenceOptions = computed(() => {
    return allRecurrenceOptions.value.map(ro => {
        return {
            id: ro.id,
            name: ro.description,
        };
    });
});

onMounted(() => {
    initBaseData({
        courses: true,
        courseGroups: true,
        domain: true,
        recurrenceOptions: true,
    });
    courseId.value = props.courseId;
    initCourseData();

});

</script>

<style scoped>

</style>
