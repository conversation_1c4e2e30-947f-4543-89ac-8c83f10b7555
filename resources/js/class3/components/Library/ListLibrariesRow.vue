<template>
    <tr :class="{ 'active': isActive }">
        <td>
            <!-- delete -->
            <button
                class="btn btn-danger btn-sm"
                data-toggle="modal"
                data-target="#del_areyousure"
                @click="libraryIdToDelete = library.id"
                v-tooltip="ucFirst(translate('generic.delete'))"
            >
                <font-awesome-icon icon="trash" />
            </button>
            <!-- edit -->
            <button
                class="btn btn-primary btn-sm"
                :class="{ 'active-button': isActive && currentChangeAction === 'edit' }"
                @click="activeLibraryId = library.id; currentChangeAction = 'edit'"
                v-tooltip="ucFirst(translate('generic.edit'))"
            >
                <font-awesome-icon icon="edit" />
            </button>
            <!-- contents -->
            <button
                class="btn btn-success btn-sm"
                :class="{ 'active-button': isActive && currentChangeAction === 'manageContents' }"
                @click="activeLibraryId = library.id; currentChangeAction = 'manageContents'"
                v-tooltip="ucFirst(translate('generic.contents'))"
            >
                <font-awesome-icon icon="list" />
            </button>
            <!-- share -->
            <button
                class="btn btn-success btn-sm"
                :class="{ 'active-button': isActive && currentChangeAction === 'manageSharing' }"
                @click="activeLibraryId = library.id; currentChangeAction = 'manageSharing'"
                v-tooltip="ucFirst(translate('generic.share'))"
            >
                <font-awesome-icon icon="share" />
            </button>
        </td>
        <td>
            {{ library.label }}
        </td>
        <td>
            {{ library?.documents?.length || 0 }}
        </td>
        <td>
            {{ shareStatus }}
        </td>
    </tr>
</template>

<script setup>
import { computed } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import useLibraries from "../../composables/useLibraries";
import useLang from "../../composables/useLang";
const { ucFirst, translate, translateChoice } = useLang();
const {
    activeLibraryId,
    currentChangeAction,
    libraryIdToDelete,
    libraryToEdit
} = useLibraries();

const props = defineProps({
    library: Object
});

const isActive = computed(() => {
    return props.library.id === activeLibraryId.value;
});

/**
 * Computed property to determine the share status of the library.
 * The library may be shared with:
 * - the whole school (prop: share_with_whole_school)
 * - a student (prop: students (array of student objects))
 * - a student group (prop: studentgroups (array of student group objects))
 * - a course (prop: courses (array of course objects))
 * - a course group (prop: coursegroups (array of course group objects))
 * It maybe shared with multiple entities (e.g. course and student)
 */
const shareStatus = computed(() => {
    let status = [];
    // if it is shared with whole school, the rest is no longer relevant    
    if (props.library.share_with_whole_school) {
        return "⚡" + ucFirst(translate('generic.sharedwithwholeschool'));
    }
    if (props.library.students.length > 0) {
        status.push("👤" + ucFirst(translateChoice('generic.sharedwithstudents', props.library.students.length, { count: props.library.students.length })));
    }
    if (props.library.studentgroups.length > 0) {
        status.push("👥 " + ucFirst(translateChoice('generic.sharedwithstudentgroups', props.library.studentgroups.length, { count: props.library.studentgroups.length })));
    }
    if (props.library.courses.length > 0) {
        status.push("⭢" + ucFirst(translateChoice('generic.sharedwithcourses', props.library.courses.length, { count: props.library.courses.length })));
    }
    if (props.library.coursegroups.length > 0) {
        status.push("⮆" + ucFirst(translateChoice('generic.sharedwithcoursegroups', props.library.coursegroups.length, { count: props.library.coursegroups.length })),);
    }
    if (status.length === 0) {
        return ucFirst(translate('generic.libraryisnotshared'));
    }
    return status.join(', ');
});

</script>

<style scoped lang="scss">
@import '../../../../sass/tmpl3/variables';
.active {
    background-color: $classbgblue;
    color: white;
}
.active-button {
    background-color: $classlight;
    color: black;
}

</style>
