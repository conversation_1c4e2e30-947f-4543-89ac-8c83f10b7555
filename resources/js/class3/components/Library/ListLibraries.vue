<template>
<div>
    <div
        v-if="libraries.length > 0"
        class="table-container"
    >
        <table class="table mt-2">
            <thead>
            <tr>
                <th>{{ ucFirst(translate("generic.functions")) }}</th>
                <th>{{ ucFirst(translate("generic.title")) }}</th>
                <th>{{ ucFirst(translateChoice("generic.documents", 2)) }}</th>
                <th>{{ ucFirst(translate("generic.remarks")) }}</th>
            </tr>
            </thead>
            <tbody class="scrollable-body">
                <ListLibrariesRow 
                    v-for="library in libraries"
                    :key="library.id"
                    :library="library" 
                />
            </tbody>
        </table>
    </div>
    <div v-else>
        <p>{{ translate('generic.nolibrariesfound') }}</p>
    </div>
    <are-you-sure
        :button-text="ucFirst(translate('generic.deletelibrary'))"
        modal-id="del_areyousure"
        @confirmclicked="removeLibrary"
    />
</div>
</template>

<script setup>
import useLibraries from "../../composables/useLibraries";
import useLang from "../../composables/useLang";
import AreYouSure from '../Layout/AreYouSure.vue';
import ListLibrariesRow from "./ListLibrariesRow.vue";

const { ucFirst, translate, translateChoice } = useLang();
const { libraries, removeLibrary } = useLibraries();

</script>

<style scoped>

</style>
