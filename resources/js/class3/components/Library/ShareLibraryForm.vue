<template>
<div>
    <strong>{{ ucFirst(translate("generic.sharing")) }}</strong>
    <div class="d-flex justify-content-between">
        <p>{{ ucFirst(translate("generic.sharewithwholeschool")) }}</p>
        <material-switch
            label-on=""
            switch-id="shareWholeSchool"
            v-model="shareWholeSchool"
            value-type="boolean"
        />
    </div>
    <template v-if="!shareWholeSchool && libraryToManageShares.id">
        <hr>
        <p>{{ ucFirst(translate("generic.share_with")) }}</p>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.courses", 2)) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareCoursesSelected"
                    :options="coursesOptionsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select courses"
                    valueProp="id"
                    label="label"
                />
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translate("generic.coursegroups")) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareCourseGroupsSelected"
                    :options="courseGroupsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select course groups"
                    valueProp="id"
                    label="label"
                />
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.students", 2)) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareStudentsSelected"
                    :options="studentsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select students"
                    valueProp="id"
                    label="label"
                />
            </div>
        </div>
        <div class="row">
            <div class="col-4">&bull;&nbsp;{{ ucFirst(translateChoice("generic.studentgroups")) }}</div>
            <div class="col-8">
                <Multiselect
                    v-model="shareStudentGroupsSelected"
                    :options="studentGroupsForSelect"
                    :searchable="true"
                    :close-on-select="false"
                    :create-option="false"
                    mode="multiple"
                    placeholder="Select student groups"
                    valueProp="id"
                    label="label"
                />
            </div>
        </div>
    </template>
    <hr>
    <div class="d-flex justify-content-end">
        <!-- Save button -->
        <button class="btn btn-primary" @click="saveSharesLibrary">
            {{ ucFirst(translate("generic.save")) }}
        </button>
    </div>
</div>
</template>

<script setup>
import { computed, onMounted, watch } from "vue";
import useLang from "../../composables/useLang";
import MaterialSwitch from "../Layout/MaterialSwitch.vue";
import useBaseData from "../../composables/useBaseData";
import Multiselect from '@vueform/multiselect';
import '@vueform/multiselect/themes/default.css';
import useLibraries from "../../composables/useLibraries";

const { allCourses, allCourseGroups, allStudentGroups, allStudents, initBaseData } = useBaseData();
const { ucFirst, translate, translateChoice } = useLang();
const {
    saveSharesLibrary,
    libraryToManageShares,
    shareWholeSchool,
    shareCoursesSelected,
    shareCourseGroupsSelected,
    shareStudentGroupsSelected,
    shareStudentsSelected
} = useLibraries();

/**
 * strip the api data to only include the id and name
 */
const coursesOptionsForSelect = computed(() => {
    return allCourses.value.map(course => ({
        id: course.id,
        label: `${course.name} ${course.recurrenceoption}`
    }));
});
const courseGroupsForSelect = computed(() => {
    return allCourseGroups.value.map(courseGroup => ({
        id: courseGroup.id,
        label: courseGroup.name
    }));
});
const studentsForSelect = computed(() => {
    return allStudents.value.map(student => ({
        id: student.id,
        label: student.name
    }));
});
const studentGroupsForSelect = computed(() => {
    return allStudentGroups.value.map(studentGroup => ({
        id: studentGroup.id,
        label: studentGroup.name
    }));
});

onMounted(() => {
    initBaseData({
        courses: true,
        courseGroups: true,
        studentGroups: true,
        students: true
    }).then(() => {
        initLibraryShares();
    });
});
const initLibraryShares = () => {
    shareWholeSchool.value = !!libraryToManageShares.value.share_with_whole_school; // convert initial value to boolean
    shareCoursesSelected.value = libraryToManageShares.value.courses
        ? libraryToManageShares.value.courses.map(course => course.id)
        : [];
    shareCourseGroupsSelected.value = libraryToManageShares.value.coursegroups
        ? libraryToManageShares.value.coursegroups.map(courseGroup => courseGroup.id)
        : [];
    shareStudentsSelected.value = libraryToManageShares.value.students
        ? libraryToManageShares.value.students.map(student => student.id)
        : [];
    shareStudentGroupsSelected.value = libraryToManageShares.value.studentgroups
        ? libraryToManageShares.value.studentgroups.map(studentGroup => studentGroup.id)
        : [];
};

watch(libraryToManageShares, () => {
    initLibraryShares();
});
</script>

<style scoped>

</style>
