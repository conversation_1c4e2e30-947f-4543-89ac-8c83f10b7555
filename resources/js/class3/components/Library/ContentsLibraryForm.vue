<template>
<div>
    <!-- List current documents -->
    <table class="table table-sm">
        <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.functions')) }}</th>
                <th>{{ ucFirst(translateChoice('generic.documents', 2)) }}</th>
                <th>{{ ucFirst(translate('generic.type')) }}</th>
                <th>{{ ucFirst(translate('generic.download')) }}</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="doc in libraryToManageContents.documents" :key="doc.id">
                <td>
                    <button class="btn btn-danger btn-sm" @click="removeDocumentFromLibrary(libraryToManageContents.id, doc.id)">
                        {{ ucFirst(translate('generic.unlink')) }}
                    </button>
                </td>
                <td>{{ doc.label }}</td>
                <td>
                    {{ doc.type }}
                    <font-awesome-icon v-if="doc.type === 'url'" icon="link" />
                    <font-awesome-icon v-else icon="file-pdf" />
                </td>
                <td>
                    <a v-if="doc.type === 'url'" class="btn btn-block btn-outline-success btn-sm" :href="doc.url" target="_blank">
                        <font-awesome-icon icon="link" />
                        {{ ucFirst(translate('generic.followlink')) }}
                    </a>
                    <a v-else class="btn btn-block btn-outline-primary btn-sm" :href="`/file/${doc.file_name}`">
                        <font-awesome-icon icon="download" />
                        {{ ucFirst(translate('generic.download')) }}
                    </a>
                </td>
            </tr>
        </tbody>
    </table>

    <!-- choose an existing document -->
    <div>
        <strong>{{ ucFirst(translate("generic.findDocumentToLinkToLibrary")) }}</strong>
        <input type="text" v-model="documentSearchKey" class="form-control" placeholder="Search document">
    </div>  
    <div v-if="documentSearchKey.length > 0">
        <table class="table table-sm" v-if="filteredDocuments.length > 0">
            <tbody>
                <tr v-for="doc in filteredDocuments" :key="doc.id">
                    <td>
                        <button class="btn btn-success btn-sm" @click="addDocumentToLibrary(libraryToManageContents.id, doc.id)">
                            {{ ucFirst(translate('generic.link')) }}
                        </button>
                    </td>
                    <td>{{ doc.label }}</td>
                </tr>
            </tbody>
        </table>
        <div v-else>
            {{ ucFirst(translate('generic.nodocumentsfound')) }}
        </div>
    </div>
    <!-- upload a new document -->
    <div>
        <strong>-{{ translate("generic.or") }}-<br>{{ ucFirst(translate('generic.uploaddocandlink')) }}</strong>
        <UploadForm :initiateUploadButtonText="ucFirst(translate('generic.startupload'))" @uploadSuccess="linkToLibrary"/>
    </div>
</div>
</template>

<script setup>
import { watch } from "vue";
import UploadForm from "./UploadForm.vue";
import useLibraries from "../../composables/useLibraries";
import useDocuments from "../../composables/useDocuments";
import useLang from "../../composables/useLang";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { libraryToManageContents } = useLibraries();
const { addDocumentToLibrary, documentSearchKey, filteredDocuments, getDocuments, removeDocumentFromLibrary } = useDocuments();
const { translate, translateChoice, ucFirst } = useLang();

watch(libraryToManageContents, () => {
    getDocuments(libraryToManageContents.value.id);
}, { immediate: true });

const downloadDocument = (docId) => {
    console.log("Downloading document with id: ", docId);
}

const linkToLibrary = (docId) => {
    console.log("Linking document with id: ", docId);
    addDocumentToLibrary(libraryToManageContents.value.id, docId);
}
</script>

<style scoped>

</style>
