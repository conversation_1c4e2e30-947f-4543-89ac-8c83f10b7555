<template>
    <panel :busy="busy">
        <template v-slot:title>
            <i class="fa fa-clipboard"></i>
            {{ ucFirst(translateChoice('generic.attendanceoptions', 2)) }}<br>
            <small>{{ translate('generic.explainattendanceoptions') }}</small>
        </template>
        <template v-slot:subtitle>
            <button
                class="btn btn-success btn-sm"
                @click="attendanceoptionIdToEdit = 0"
                data-toggle="modal"
                data-target="#editAttandanceOption"
            >
                {{ ucFirst(translate('generic.newattendanceoption'))}}
            </button>
        </template>
        <div v-if="errorMessage !== ''" class="alert alert-warning">{{ errorMessage }}</div>
        <table class="table">
            <thead>
            <tr>
                <th>{{ ucFirst(translate('generic.functions')) }}</th>
                <th>{{ ucFirst(translate('generic.label')) }}</th>
                <th>{{ ucFirst(translate('generic.actiontutor')) }}</th>
            </tr>
            </thead>
            <tbody>
            <tr v-for="attendanceOption in attendanceoptions" :key="attendanceOption.id">
                <td>
                    <button
                        @click="attendanceoptionIdToDelete = attendanceOption.id"
                        data-toggle="modal"
                        data-target="#delAttendanceoption"
                        class="btn btn-sm btn-danger"
                    >
                        <i class="fa fa-trash"></i>
                    </button>
                    <button
                        @click="attendanceoptionIdToEdit = attendanceOption.id"
                        data-toggle="modal"
                        data-target="#editAttandanceOption"
                        class="btn btn-sm btn-success"
                    >
                        <i class="fa fa-edit"></i>
                    </button>
                </td>
                <td>{{ attendanceOption.label }}</td>
                <td>{{ attendanceOption.action_tutor }}</td>
            </tr>
            </tbody>
        </table>
    </panel>
    <EditAttendanceoption />
    <!-- Confirm delete attendance option -->
    <are-you-sure
        :button-text    = "`${ucFirst(translate('generic.delete'))} ${translateChoice('generic.attendanceoptions', 1)}`"
        @confirmclicked = "deleteAttendanceoption"
        modal-id        = "delAttendanceoption"
    ></are-you-sure>
</template>

<script setup>
import { onMounted } from 'vue';
import Panel from '../Layout/Panel.vue';
import useLang from '../../composables/useLang';
import useAttendanceoptions from '../../composables/useAttendanceoptions';
import EditAttendanceoption from './EditAttendanceoption.vue';
import AreYouSure from '../Layout/AreYouSure';

const { ucFirst, translate, translateChoice } = useLang();
const {
    attendanceoptionIdToDelete,
    attendanceoptionIdToEdit,
    attendanceoptions,
    deleteAttendanceoption,
    errorMessage,
    busy,
    getAll
} = useAttendanceoptions();
// init with DB contents
onMounted(() => {
    console.log('mounted');
    getAll();
});
</script>

<style scoped>

</style>
