<template>
<div>
    <template v-if="isAdmin">
        <div v-if="student?.has_access" class="alert alert-success">
            <FontAwesomeIcon icon="fa-solid fa-check" />
            <span v-html="translate('generic.studenthasaccesstoprefspage', {href: `/schedulepreference/${student?.accesstoken}`})"></span>
        </div>
        <div v-else class="alert alert-warning">
            <FontAwesomeIcon icon="fa-solid fa-lock" />
            {{ translate('generic.pleasenotestudenthasnoaccess') }}
        </div>
    </template>
    <template v-else>
        <CompanyHeader />
    </template>
    <div class="d-flex flex-column flex-lg-row">
        <div class="flex-grow-1">
            <ActiveCourses />
        </div>
        <div v-if="!isAdmin" class="flex-shrink-0 col-lg-4 ml-lg-3">
            <!-- ask students to check their address data in the database -->
            <StudentAddressCheck :student="student"/>
        </div>
    </div>
    <PlanningPreferences />
</div>
</template> 

<script setup>
import { onMounted } from "vue";
import useSchedulePreferences from "../../composables/useSchedulePreferences";
import useLang from "../../composables/useLang";
import ActiveCourses from './ActiveCourses.vue';
import PlanningPreferences from './PlanningPreferences.vue';
import StudentAddressCheck from "../Students/StudentAddressCheck.vue";
import CompanyHeader from '../Layout/CompanyHeader.vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { getStudentPrefs, isAdmin, student } = useSchedulePreferences();
const { translate } = useLang();
onMounted(() => {
    getStudentPrefs();
});
</script>

<style scoped>

</style>
