<template>
    <!-- We no longer edit date exception in this interface so no need to check if type = date exception -->
    <modal
        modal-id="editEvent"
        size="large"
        :popup-title="ucFirst(translate('generic.editevent'))"
        :closetext="ucFirst(translate('generic.cancel'))"
        @closeBtnClicked="doRevert"
        :is-draggable="true"
    >
        <div v-if="eventToEdit">
            <div class="row">
                <div class="col-md-3"><label>{{ ucFirst(translate('generic.course')) }}</label></div>
                <div class="col-md-9">
                    <span class="mr-1">{{ eventToEdit.courseName }}:</span>
                    <span v-if="eventToEdit.isAStudentGroup">
                        {{ eventToEdit.nrOfStudents }} {{ translateChoice('generic.students', eventToEdit.nrOfStudents) }}
                        <button
                            v-if="eventToEdit.nrOfStudents > 0"
                            class="btn btn-primary btn-sm ml-2"
                            @click="isOpen = !isOpen"
                            type="button"
                            aria-controls="show list of students in course"
                        >
                            <span v-show="isOpen">
                                <i class="fa fa-arrow-circle-up"></i>
                                {{ translate('generic.close') }}
                            </span>
                            <span v-show="!isOpen">
                                <i class="fa fa-arrow-circle-down"></i>
                                {{ translate('generic.open') }}
                            </span>
                        </button>
                        <div
                            v-if="eventToEdit.nrOfStudents > 0"
                            :class="['collapse', {show: isOpen}]"
                            id="studentsList"
                        >
                            <span v-for="student in studentNames" :key="student.id"
                                  class="badge badge-primary mr-1">
                                <a :href="'/students/' + student.id + '/edit'" class="link-on-badge">
                                    {{ student.name }}
                                    <i class="fa fa-id-card"></i>
                                </a>
                            </span>
                        </div>
                        <a :href="'/students/' + eventToEdit.studentId + '/edit'" class="ml-2">
                            {{ eventToEdit.studentName }}
                            <i class="fa fa-id-card"></i>
                        </a>
                    </span>
                    <span v-else>
                        <a :href="'/students/' + eventToEdit.studentId + '/edit'">
                            {{ eventToEdit.studentName }}
                            <i class="fa fa-id-card"></i>
                        </a>
                    </span>
                </div>
            </div>

            <!-- tutor -->
            <div class="row">
                <div class="col-md-3"><label>{{ ucFirst(translateChoice('generic.tutors', 1)) }}</label></div>
                <div class="col-md-9">
                    <select class="form-control" v-model="newTutorId">
                        <option v-for="tutor in allTutors" :key="tutor.id" :value="tutor.id">
                            {{ tutor.name }}
                        </option>
                    </select>
                </div>
            </div>

            <!-- location -->
            <div class="row">
                <div class="col-md-3"><label>{{ ucFirst(translateChoice('generic.locations', 1)) }}</label></div>
                <div class="input-group col-md-9">
                <span class="input-group-text" id="basic-addon1">
                    <span v-html="chosenLocationIcon"></span>
                </span>
                    <select class="form-control" v-model="newLocationId">
                        <option v-for="location in allLocations" :key="location.id" :value="location.id">
                            {{ location.name }}
                        </option>
                    </select>
                </div>
            </div>

            <hr/>

            <!-- PLANNING -->
            <!-- start -->
            <div class="row">
                <div class="col-md-3">
                    <label for="startdate">
                        {{ ucFirst(translate('generic.startdate')) }}
                    </label>
                </div>
                <div class="col-md-3">
                    <VueDatepicker
                        v-model="newStartDate"
                        v-bind="dpOptionsDate"
                    />
                </div>
                <div class="col-md-3">
                    <label for="starttime">
                        {{ ucFirst(translate('generic.starttime')) }}
                    </label>
                </div>
                <div class="col-md-3">
                    <VueDatepicker
                        v-model="newStartTime"
                        format="HH:mm"
                        :locale="dpOptions.locale"
                        :placeholder="translate('generic.time')"
                        time-picker
                        model-type="format"
                    />
                </div>
            </div>
            <!-- end -->
            <div class="row">
                <div class="col-md-3">
                    <label>
                        {{ ucFirst(translate('generic.enddate')) }}
                    </label>
                </div>
                <div class="col-md-3">
                    <input class="form-control input-date-time-field" readonly :value="newEndDate">
                </div>
                <div class="col-md-3">
                    <label>
                        {{ ucFirst(translate('generic.endtime')) }}
                    </label>
                </div>
                <div class="col-md-3">
                    <input class="form-control input-date-time-field" :readonly="freezeTimespan"
                           v-model="newEndTime">
                </div>
            </div>
            <div class="row">
                <div
                    class='col-md-6 offset-md-6'
                    v-if="freezeTimespan"
                    v-html="translate('generic.explainonlysingleevent')"/>
            </div>
            <!-- if event - recurring : ends_after_nr_of_occurences (if this is a recurring event) -->
            <!-- check if the event is still part of the series -->
            <hr v-if="!eventToEdit.isSolitary"/>
            <div class="row" v-if="!eventToEdit.isSolitary">
                <div class="col-md-3">
                    <label class="mt-2 mb-2">{{ ucFirst(translate("generic.applyto")) }}</label>
                </div>
                <div class="col-md-9">
                    <div class="col-xs-12 col-sm-12 col-md-12">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="updateRecurrence"
                                   id="updateRecurrenceThisEvent"
                                   value="thisEvent" v-model="updateOccurrence">
                            <label class="form-check-label neplink" for="updateRecurrenceThisEvent">
                                {{ ucFirst(translate('generic.thisevent')) }}
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="updateRecurrence"
                                   id="updateRecurrenceThisAndFuture"
                                   value="thisAndFutureEvents" v-model="updateOccurrence">
                            <label class="form-check-label neplink" for="updateRecurrenceThisAndFuture">
                                {{ ucFirst(translate('generic.thisandfutureevent')) }}
                            </label>
                            <span v-tooltip="translate('generic.explainreschedule')">
                                <i class="fa fa-lg fa-exclamation-triangle text-danger"></i>
                            </span>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="updateRecurrence"
                                   id="updateRecurrenceAllEvents"
                                   value="allEvents" v-model="updateOccurrence">
                            <label class="form-check-label neplink" for="updateRecurrenceAllEvents">
                                {{ ucFirst(translate('generic.allevents')) }}
                            </label>
                            <span v-tooltip="translate('generic.explainreschedule')">
                                <i class="fa fa-lg fa-exclamation-triangle text-danger"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <hr>
            <!-- Attendance -->
            <template
                v-if="eventToEdit.nrOfStudents > 0"
            >
                <div class="row">
                    <div class="col-12">
                        <label>{{ ucFirst(translate('generic.attendance')) }}</label>
                        <button
                            class="btn btn-primary btn-sm ml-2"
                            @click="toggleOpenSectionAttendance"
                            type="button"
                        >
                            <!-- use v-show, because the fontawesome svg elements will be presents while using v-if does nothing -->
                             <span v-show="openSectionAttendance">
                                <i class="fa fa-arrow-circle-up"></i>
                            </span>
                            <span v-show="!openSectionAttendance">
                                <i class="fa fa-arrow-circle-down"></i>
                            </span>
                            {{ openSectionAttendance ? translate('generic.close') : translate('generic.open') }}
                        </button>
                    </div>
                </div>
                <div v-if="openSectionAttendance">
                    <hr>
                    <div v-for="student in eventToEdit.students" :key="student.id" class="row">
                        <div class="col-3">
                            {{ student.name }}
                        </div>
                        <div class="col-9">
                            <select class="form-control" v-model="student.attendance.attendanceoption_id">
                                <option value="0">{{ ucFirst(translate('generic.pleasechooseanoption')) }}</option>
                                <option v-for="attoption in allAttendanceoptions" :key="attoption.id" :value="attoption.id">
                                    {{ ucFirst(attoption.label) }}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </template>
            <!-- Remarks -->
            <hr>
            <div class="row">
                <div class="col">
                    <label>{{ ucFirst(translate('generic.remarks')) }}</label>
                    <textarea class="form-control" v-model="newRemarks"></textarea>
                </div>
            </div>
            <hr>
            <!-- Send to ClassE -->
            <div class="row">
                <div class="col-md-3">
                    <label class="mt-2" v-html="ucFirst(translate('generic.sendmessagetoclasse'))"/>
                </div>
                <div class="col-md-9 mt-2">
                    <div class="row">
                        <div class="col-12">
                            <material-switch
                                label-on=""
                                switch-id="sendChangeToClassE"
                                v-model="sendChangeToClassE"
                            >
                            </material-switch>
                        </div>
                    </div>
                    <template v-if="sendChangeToClassE">
                        <div class="row mt-2">
                            <div class="col">
                                <label>{{ ucFirst(translate('generic.recipient')) }}:</label>
                                <span v-if="tutorChanged">{{ newTutor.name }} {{
                                        translate('generic.and')
                                    }} {{ oldTutor.name }}</span>
                                <span v-else>{{ oldTutor.name }}</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col">
                                <label>{{ ucFirst(translateChoice('generic.messages', 1)) }}</label>
                                <textarea class="form-control" v-model="sendMessageBody"></textarea>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <div class="row">
                <!-- Publish as event on website -->
                <div class="col-md-3">
                    <label class="mt-2" v-html="ucFirst(translate('generic.publishevent'))"/>
                </div>
                <div class="col-md-3 mt-2">
                    <div class="row">
                        <div class="col-12">
                            <material-switch
                                label-on=""
                                switch-id="publishEvent"
                                v-model="publishEvent"
                            >
                            </material-switch>
                        </div>
                    </div>
                </div>
                <!-- Sticky -->
                <div class="col-md-3">
                    <label class="mt-2" v-html="ucFirst(translate('generic.stickyflag'))"/>
                </div>
                <div class="col-md-3 mt-2">
                    <div class="row">
                        <div class="col-12">
                            <material-switch
                                label-on=""
                                switch-id="sticky"
                                v-model="flagSticky"
                            >
                            </material-switch>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" v-if="hasErrors || hasWarnings">
                <div class="col-6">
                    &nbsp;
                </div>
                <div class="col-6">
                    <p>{{ eventToEdit.flags.errors.join(',') }}</p>
                    <p>
                        {{ eventToEdit.flags.warnings.join(',') }}
                        <span v-if="flagSticky"> - {{ translate('generic.notactive') }}</span>
                    </p>
                </div>
            </div>
        </div>
        <div v-else>
            <h3>No event received (yet)...</h3>
        </div>
        <!--  -->
        <template #okbutton>
            <button type="button" class="btn btn-primary"
                    @click.prevent="saveEditEvent"
                    :disabled="okButtonDisable"
            >
                {{ translate('generic.confirmsavechangeevent') }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import { computed, onMounted, ref, watch } from "vue";
import useEditEvents from "../../composables/useEditEvent";
import useBaseData from "../../composables/useBaseData";
import useDatePicker from "../../composables/useDatePicker";
import useLang from "../../composables/useLang";
import useNoty from "../../composables/useNoty";
import useDateTime from "../../composables/useDateTime";
import VueDatepicker from '@vuepic/vue-datepicker';
import Modal from '../Layout/Modal3';
import '@vuepic/vue-datepicker/dist/main.css';
import moment from 'moment';
import MaterialSwitch from '../Layout/MaterialSwitch';
import $ from 'jquery';
import axios from 'axios';

const isOpen = ref(false);
const { doRevert, eventToEdit, studentNames } = useEditEvents();
const { allLocations, allTutors, allAttendanceoptions, initBaseData } = useBaseData();
const { dpOptions } = useDatePicker(false, null, true); // never "wholeday", this is an event, not a date exception
const { dpOptions: dpOptionsDate } = useDatePicker(true, null, true); // date-only picker
const { displayDateTime, displayTime } = useDateTime();
const { failNoty, successNoty } = useNoty();
const { ucFirst, translate, translateChoice } = useLang();
const newTutorId = ref(0);
const newLocationId = ref(0);
const newStartDate = ref('');
const newStartTime = ref('');
const newEndTime = ref('');
const newRemarks = ref('');
const updateOccurrence = ref('thisEvent');
const sendChangeToClassE = ref();
const publishEvent = ref(false);
const flagSticky = ref(false);
const sendMessageBody = ref('');
const openSectionAttendance = ref(false);
const renderKey = ref(0);

const emit = defineEmits(['updateCalendarEvents']);

onMounted(async () => {
    await initBaseData({
        locations: true,
        tutors: true,
        attendanceOptions: true
    });
    initComponentData();
});

const tutorChanged = computed(() => {
    return parseInt(newTutorId.value) !== parseInt(eventToEdit.value?.tutorId);
});
const oldTutor = computed(() => {
    return allTutors.value.find(tutor => tutor.id === eventToEdit.value?.tutorId);
});
const newTutor = computed(() => {
    return allTutors.value.find(tutor => tutor.id === newTutorId.value);
});

const toggleOpenSectionAttendance = () => {
    openSectionAttendance.value = !openSectionAttendance.value;
    renderKey.value++;
};

/**
 * initialize message body with current data
 */
watch(sendChangeToClassE, () => {
    if (sendMessageBody.value === '') {
        sendMessageBody.value = ucFirst(translate('generic.appointmentofhaschanged', {
            studentName: eventToEdit.value?.studentName,
            datetime: `${moment(eventToEdit.value?.start).format('DD-MM-YYYY')} ${translate('generic.at')} ${moment(eventToEdit.value?.start).format('HH:mm')}`
        }));
    }
});

/**
 * update end-time based on timespan of event
 * ignoring if the end-time has already been changed manually
 */
watch(newStartTime, () => {
    const tsParts = eventToEdit.value.timespan.split(' ');
    newEndTime.value = moment(newStartTime.value, 'HH:mm').add(tsParts[0], tsParts[1]).format('HH:mm');
});

/**
 * initialize local (modifiable) variables on eventToEdit ready
 */
watch(eventToEdit, () => {
   initComponentData();
});

const initComponentData = () => {
    newTutorId.value = eventToEdit.value?.tutorId ?? 0;
    newLocationId.value = eventToEdit.value?.locationId ?? 0;
    newStartDate.value = eventToEdit.value?.start ? moment(eventToEdit.value?.start, 'YYYY-MM-DD HH:mm').format('DD-MM-YYYY') : '';
    newStartTime.value = eventToEdit.value?.start ? moment(eventToEdit.value?.start).format('HH:mm') : '';
    newEndTime.value = eventToEdit.value?.end ? moment(eventToEdit.value?.end).format("HH:mm") : '';
    newRemarks.value = eventToEdit.value?.remarks ?? '';
    publishEvent.value = eventToEdit.value?.flag_publish ?? false;
    flagSticky.value = eventToEdit.value?.flag_sticky ?? false;
    sendMessageBody.value = '';
    sendChangeToClassE.value = false;
    openSectionAttendance.value = false;
};

/**
 * pickup the icon of the chosen location
 * @type {ComputedRef<string>}
 */
const chosenLocationIcon = computed(() => {
    return allLocations.value.find(loc => loc.id === newLocationId.value)?.iconSmall;
});

const okButtonDisable = computed(() => {
    return (updateOccurrence.value == null || updateOccurrence.value === '') ||
        (sendChangeToClassE.value && sendMessageBody.value === '');
});

const saveEditEvent = async () => {
    // close popup
    $('#editEvent').modal('hide');
    // call save action
    try {
        if (sendChangeToClassE.value) {
            const recipients = [];
            recipients.push(newTutorId.value);
            // tutor changed: send it to both old and new tutor
            if (tutorChanged.value) {
                recipients.push(oldTutor.value.id);
            }
            messageClassE(
                recipients,
                ucFirst(translate('generic.appointmentchanged')),
                sendMessageBody.value
            );
        }
        const resp = await saveEditedEvent();
        successNoty(
            resp.data.result ?? ucFirst(translate('generic.newplanningsaved')),
            ucFirst(translate('generic.success')), 5000);
        emit('updateCalendarEvents');
    } catch (error) {
        failNoty(
            ucFirst(translate('generic.savingfailed')) + `: ${error}`,
            ucFirst(translate('generic.failed')));
        emit('updateCalendarEvents');
    }
};

/**
 * Save a message for one or more tutors so ClassE can pick it up
 * Messages from this component will always be directed to a tutor and initiated by an admin
 * @param recipients array of id
 * @param subject
 * @param body
 */
const messageClassE = (recipients, subject, body) => {
    const fromType = 'admin';
    const toType = 'tutor';
    recipients.forEach((recipient) => {
        const data = {
            toId: recipient,
            fromType,
            toType,
            subject,
            body
        };
        axios.post('/api/messages', data)
            .then(response => {
                const tutor = allTutors.value.find(tutor => tutor.id === recipient);
                successNoty(ucFirst(translate("generic.messagesentto", { recipient: tutor.name })));
            })
            .catch(err => {
                failNoty(ucFirst(translate('generic.messagesentfailed')) + ': ' + err);
            });
    });
};

/**
 * actual save action (the ajax call)
 * @returns {Promise<AxiosResponse<any>>}
 */
const saveEditedEvent = () => {
    let startDate = newStartDate.value;
    let endDate = newEndDate.value; // This is now a computed property
    // dates must be in mysql format
    if (startDate.substring(2, 3) === '-') {
        startDate = moment(startDate, 'DD-MM-YYYY').format('YYYY-MM-DD');
    }
    if (endDate != null && endDate.substring(2, 3) === '-') {
        endDate = moment(endDate, 'DD-MM-YYYY').format('YYYY-MM-DD');
    }

    const data = {
        startdate: startDate,
        starttime: newStartTime.value,
        enddate: endDate,
        endtime: newEndTime.value,
        applyTo: updateOccurrence.value, // how many to update: 'this', 'all' or 'only future events'
        tutors: [newTutorId.value],
        locationId: newLocationId.value,
        remarks: newRemarks.value,
        flag_publish: publishEvent.value,
        timetable_id: eventToEdit.value.timetable_id,
        attendanceoptions: eventToEdit.value.isAStudentGroup ? eventToEdit.value.students.map(student => {
            return {
                studentId: student.id,
                attendanceoptionId: student.attendance?.attendanceoption_id ?? 0
            };
        }) : [{
            studentId: eventToEdit.value?.studentId,
            attendanceoptionId: eventToEdit.value.students[0]?.attendance?.attendanceoption_id ?? 0
        }]
    };
    return axios.put(`/api/calendarevent/${eventToEdit.value?.eventId}`, data);
};

const freezeTimespan = computed(() => {
    return updateOccurrence.value !== 'thisEvent';
});

const hasErrors = computed(() => {
    return eventToEdit.value?.flags?.errors?.length > 0;
});
const hasWarnings = computed(() => {
    return eventToEdit.value?.flags?.warnings?.length > 0;
});

const newEndDate = computed(() => {
    if (!newStartDate.value || !eventToEdit.value) return '';
    // For most events, the end date is the same as the start date
    // The time difference is handled by newEndTime
    return newStartDate.value;
});

</script>

<style scoped>
.input-group-text {
    background-color: transparent;
}

.input-date-time-field {
    font-size: 0.9rem;
}

a {
    text-decoration: none;
}

.link-on-badge {
    color: white;
}
</style>
