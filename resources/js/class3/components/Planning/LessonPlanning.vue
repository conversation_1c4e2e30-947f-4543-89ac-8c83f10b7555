<template>
  <div class="planning">
    <div class="row">
      <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column"><lesson-planning-base-data class="flex-fill" /></div>
      <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column"><lesson-planning-details class="flex-fill" /></div>
      <div class="col-xs-12 col-xl-4 d-xl-flex flex-xl-column"><lesson-planning-create class="flex-fill" /></div>
    </div>
    <div>
      <lesson-planning-event-series />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from "vue";
import useBaseData from "../../composables/useBaseData";
import LessonPlanningBaseData from './LessonPlanningBaseData.vue';
import LessonPlanningDetails from './LessonPlanningDetails.vue';
import LessonPlanningCreate from './LessonPlanningCreate.vue';
import LessonPlanningEventSeries from './LessonPlanningEventSeries.vue';

const { initBaseData } = useBaseData();

onMounted(async () => {
    await initBaseData({
        locations: true,
        tutors: true,
        schoolYears: true,
        courses: true,
    });
});
</script>

<style scoped>
.planning {
  margin-top: -20px;
}
</style>
