<template>
    <Panel :busy="busy">
        <template #title>
            <h3>{{ ucFirst(translate('generic.reportappointments')) }}</h3>
        </template>
        <div class="row">
            <div class="col-12">
                <individual-students-timetables />
            </div>
            <div class="col-12 mt-4">
                <group-timetables />
            </div>
        </div>
    </Panel>
</template>

<script setup>
import { onMounted } from "vue";
import useLang from "../../composables/useLang";
import useTimetableReport from "../../composables/useTimetableReport";
import Panel from "../Layout/Panel.vue";
import IndividualStudentsTimetables from "./IndividualStudentsTimetables.vue";
import GroupTimetables from "./GroupTimetables.vue";

const { ucFirst, translate } = useLang();
const { busy, fetchTimetablesData } = useTimetableReport();

onMounted(() => {
    fetchTimetablesData();
});
</script>

<style scoped>
/* Any component-specific styles can go here */
</style>
