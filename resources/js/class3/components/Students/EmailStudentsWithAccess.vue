<template>
    <modal 
        :modal-id           = "modalId"
        :popup-title        = "ucFirst(translate('generic.emailstudentswithaccess'))"
        :closetext          = "ucFirst(translate('generic.cancel'))"
        size                = "large"
    >
        <template #okbutton>
            <button
                class="btn btn-primary"
                @click="sendmailmulti"
                :disabled="!enableEmailSending"
            >
                <font-awesome-icon icon="fa-solid fa-paper-plane" />
                {{ ucFirst(translate('generic.send')) }}
            </button>
        </template>
        <div class="row">
            <div class="col-md-2"><label>{{ ucFirst(translate('generic.to')) }}</label></div>
            <div class="col-md-10">
                <span>
                    {{students.length}} {{ translateChoice('generic.students', students.length) }}
                </span>
                <button 
                    class="btn btn-sm btn-primary ml-2" 
                    @click="toggleShowRecipients"
                >
                    <font-awesome-icon icon="fa-solid fa-eye" />
                    {{ ucFirst(translate('generic.showrecipients')) }}
                </button>

                <Transition name="fade">
                    <div v-if="showRecipients" class="mt-2 mb-2">
                        <span class="mr-2" v-for="student in students">
                            [<a :href="`/students/${student.id}/edit`">{{student.name}}</a>]
                        </span>
                    </div>
                </Transition>
            </div>
        </div>

        <div class="row">
            <div class="col-md-2">
                <label for="frommulti">{{ ucFirst(translate('generic.from')) }}</label>
            </div>
            <div class="col-md-10">
                <input id="frommulti" name="from" class="form-control" v-model="from" />
            </div>
        </div>

        <div class="row">
            <div class="col-md-2">     
                <label for="subject">{{ ucFirst(translate('generic.subject')) }}</label>
            </div>
            <div class="col-md-10">
                <input type="text" class="form-control" id="subject" v-model="subject" />
            </div>
        </div>
        <div class="row">
            <div class="col-md-2">
                <label for="templatemulti">
                    {{ucFirst(translate('generic.templates'))}}
                </label>
            </div>
            <div class="col-md-10">
                <list-templates for-target="c" @templateChosen="templateChosen"></list-templates>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
<!--                <ckeditor -->
<!--                    :editor="editor" -->
<!--                    :config="editorConfigWithMediaAndImage"-->
<!--                    v-model="mailBody"-->
<!--                />-->
            </div>
        </div>
    </modal>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import Modal from "../Layout/Modal3.vue";
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import CKEditor from '@ckeditor/ckeditor5-vue';
import useConfigItems from '../../composables/useConfigItems';
import useLang from '../../composables/useLang';
import useEmailStudentsWithAccess from '../../composables/useEmailStudentsWithAccess';
import useBaseData from '../../composables/useBaseData';
import ListTemplates from "../Email/ListTemplates.vue";

const { from, getStudentsWithAccess, mailBody, sendmailmulti, students, subject } = useEmailStudentsWithAccess();
const { editorConfigWithMediaAndImage } = useConfigItems();
// const editor = ClassicEditor;
// const ckeditor = CKEditor.component;
const { domain, initBaseData } = useBaseData();
const { ucFirst, translate, translateChoice } = useLang();
const showRecipients = ref(false);

defineProps({
    modalId: {
        type: String,
        required: true
    }
});

onMounted(async () => {
    await initBaseData({ domain: true });
    await getStudentsWithAccess();
    subject.value = ucFirst(translate('generic.preferredtimeforlessonat', {schoolname: domain.value?.schoolName}));
    from.value = domain.value.strAddress?.email || '';
});

const templateChosen = (ev) => {
    if (ev.selectedTemplateId === -1) {
        mailBody.value = '';
    } else {
        mailBody.value = ev.selectedTemplate?.content || '';
    }
};

const toggleShowRecipients = () => {
    showRecipients.value = !showRecipients.value;
};

const enableEmailSending = computed(() => {
    return mailBody.value.length > 20 && subject.value.length > 5 && from.value.length > 5;
});

</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
:deep(.ck-editor__editable_inline) {
    min-height: 400px;
    max-height: 400px;
}
</style>
