<template>

    <!-- Revoke all access modal -->
    <modal
        :popup-title="ucFirst(translate('generic.areyousure')) + '?'"
        :modal-id="modalId"
        :closetext="ucFirst(translate('generic.cancel'))"
    >
        <strong class="mt-2">{{ ucFirst(translate('generic.revokeaccess')) }}</strong>
        <div class="mt-3">
            <label for="revoke-all" style="cursor: pointer;">
                <input type="radio" id="revoke-all" v-model="revokeFilter" value="all">
                {{ ucFirst(translate('generic.forallstudents')) }}
            </label><br>
            <label for="revoke-filter" style="cursor: pointer;">
                <input type="radio" id="revoke-filter" v-model="revokeFilter" value="filter">
                {{ ucFirst(translate('generic.explainleavesopennotrespondedyet', { days: domainThreshold })) }}*
            </label>
        </div>
        <div class="mt-3">
            <em>
                *) {{
                    ucFirst(translate("generic.explainleavesopennotrespondedyetextend", {
                        days: domainThreshold,
                        days2: domainThreshold
                    }))
                }}
            </em>
        </div>
        <template #okbutton>
            <button
                type="button"
                class="btn btn-danger"
                data-dismiss="modal"
                data-testid="confirm-revoke-all-button"
                :disabled="revokeFilter === ''"
                @click.prevent="revokeAccessToAll"
            >
                {{ ucFirst(translate('generic.revokeaccess')) }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import useStudentAccessCodes from "../../composables/useStudentAccessCodes";
import useLang from "../../composables/useLang";
import Modal from "../Layout/Modal3.vue";

defineProps({
    modalId: {
        type: String,
        required: true
    }
});
const { ucFirst, translate } = useLang();
const { domainThreshold, revokeAccessToAll, revokeFilter } = useStudentAccessCodes();

</script>

<style scoped>

</style>
