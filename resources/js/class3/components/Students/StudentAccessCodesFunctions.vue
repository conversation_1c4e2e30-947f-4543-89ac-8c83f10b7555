<template>
    <div>
        <strong>{{ ucFirst(translate('generic.functions')) }}</strong>
        <hr>
        <button 
            class="btn btn-primary mr-2 mb-2 text-left"
            data-toggle="modal"
            data-target="#confirm-grant-all-modal"
        >
            <font-awesome-icon icon="fa-solid fa-plus" />
            {{ ucFirst(translate('generic.grantaccesstoallstudents')) }}
        </button>
        <button
            class="btn btn-danger mr-2 mb-2 text-left"
            data-toggle="modal"
            data-target="#confirm-revoke-all-modal"
        >
            <font-awesome-icon icon="fa-solid fa-minus" />
            {{ ucFirst(translate('generic.revokeaccessfromallstudents')) }}
        </button>
        <button 
            class="btn btn-success mb-2 text-left" 
            data-toggle="modal"
            data-target="#email-students-with-access"
        >
            <font-awesome-icon icon="fa-solid fa-envelope" />
            {{ ucFirst(translate('generic.mailallstudents')) }}
        </button>
        <confirm-grant-multiple-access modal-id="confirm-grant-all-modal" />
        <confirm-revoke-multiple-access modal-id="confirm-revoke-all-modal"/>
        <email-students-with-access modal-id="email-students-with-access" />
    </div>
</template>

<script setup>
import ConfirmRevokeMultipleAccess from "./ConfirmRevokeMultipleAccess.vue";
import confirmGrantMultipleAccess from "./ConfirmGrantMultipleAccess.vue";
import EmailStudentsWithAccess from "./EmailStudentsWithAccess.vue";
import useLang from '../../composables/useLang';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

const { ucFirst, translate } = useLang();
</script>

<style scoped>
/* 1200: XL -> LG, on large screens, the buttons will be shown as a list below each other */
@media (min-width: 1200px) {
    .btn {
        width: 100%;
    }
}
</style>
