<template>
    <modal
        :popup-title="ucFirst(translate('generic.areyousure')) + '?'"
        :modal-id="modalId"
        :closetext="ucFirst(translate('generic.cancel'))"
    >
        <strong class="mt-2">{{ ucFirst(translate('generic.grantaccess')) }}</strong>
        <div class="mt-3">
            <label for="grant-all" style="cursor: pointer;">
                <input type="radio" id="grant-all" v-model="grantFilter" value="all">
                {{ ucFirst(translate('generic.forallstudents')) }}
            </label><br>
            <label for="grant-filter" style="cursor: pointer;">
                <input type="radio" id="grant-filter" v-model="grantFilter" value="filter">
                {{ ucFirst(translate('generic.explainopennotrespondedyet', { days: domainThreshold })) }}*
            </label>
        </div>
        <div class="mt-3">
            <em>
                *) {{
                    ucFirst(translate("generic.explainopennotrespondedyetextend", {
                        days: domainThreshold,
                        days2: domainThreshold
                    }))
                }}
            </em>
        </div>
        <template #okbutton>
            <button
                type="button"
                class="btn btn-primary"
                data-dismiss="modal"
                data-testid="confirm-grant-all-button"
                :disabled="grantFilter === ''"
                @click.prevent="grantAccessToAll"
            >
                {{ ucFirst(translate('generic.grantaccess')) }}
            </button>
        </template>
    </modal>
</template>

<script setup>
import useStudentAccessCodes from "../../composables/useStudentAccessCodes";
import useLang from "../../composables/useLang";
import Modal from "../Layout/Modal3.vue";
defineProps({
    modalId: {
        type: String,
        required: true
    }
});
const { ucFirst, translate } = useLang();
const { domainThreshold, grantAccessToAll, grantFilter } = useStudentAccessCodes();
</script>

<style scoped>

</style>
