<template>
<div>
    <edit-school-year v-if="schoolYearIDToEdit >= 0"/>
    <list-school-years />
    <floating-bar>
        <div class="btn-group" role="group" aria-label="floating-bar-functions">
            <a href='/home' class="btn btn-secondary">{{ ucFirst(translate('generic.dashboard')) }}</a>
            <button class="btn btn-primary" @click="saveSchoolyear" :disabled="!isSaveable">
                {{ ucFirst(translate('generic.save')) }}
            </button>
        </div>
    </floating-bar>
</div>
</template>

<script setup>
import { onMounted } from "vue";
import ListSchoolYears from "./ListSchoolYears.vue";
import EditSchoolYear from "./EditSchoolYear.vue";
import useBaseData from "../../composables/useBaseData";
import useSchoolYear from "../../composables/useSchoolYear";
import useLang from "../../composables/useLang";
import FloatingBar from "../Layout/FloatingBar.vue";

const { initBaseData }  = useBaseData();
const { isSaveable, schoolYearIDToEdit, saveSchoolyear } = useSchoolYear();
const { ucFirst, translate } = useLang();

const init = async () => {
    await initBaseData({schoolYears: true});
}
onMounted(async () => {
    await init();
});

</script>

<style scoped>

</style>
