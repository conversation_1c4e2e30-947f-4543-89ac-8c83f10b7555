import { ref } from 'vue';
import useLang from "./useLang.js";
import useNoty from "./useNoty.js";
import axios from 'axios';

const { ucFirst, translate } = useLang();
const { failNoty, successNoty } = useNoty();

//const chosenEmail = ref('');
const students = ref([]);
const busy = ref(false);


export default function useEmailStudentsWithAccess() {
    const chosenTemplate = ref(-1);
    const subject = ref('');
    const from = ref('');
    const mailBody = ref('');
    const getStudentsWithAccess = async () => {
        try {
            busy.value = true;
            const resp = await axios.get('/api/studentswithaccess');
            students.value = resp.data;
        } catch (error) {
            failNoty(ucFirst(translate('generic.errorretrievingstudents')) + ":" + error);
        } finally {
            busy.value = false;
        }
    }

    const sendmailmulti = async () => {
        busy.value = true;
        const data = {
            to: students.value.map(s => {
                return { id: s.id, email: s.email }
            }),
            from: from.value,
            subject: subject.value,
            body: mailBody.value,
        };
        try {
            const resp = await axios.post('/api/sendmultiemail', data);
            successNoty(translate('email.emailsent'));
        } catch(error) {
            failNoty(ucFirst(translate('email.errorsendingemail')) + ":" + error);
        } finally {
            busy.value = false;
        }
    };

    const sendmailsingle = async ( student ) => {
        busy.value = true;
        const data = {
            to: student.studentId,
            from: from.value,
            subject: subject.value,
            body: mailBody.value,
        };
        try {
            const resp = await axios.post('/api/sendsingleemail', data);
            successNoty(translate('email.emailsent'));
        } catch(error) {
            failNoty(ucFirst(translate('email.errorsendingemail')) + ":" + error);
        } finally {
            busy.value = false;
        }
    }

    return {
        busy,
        chosenTemplate,
        // chosenEmail,
        from,
        getStudentsWithAccess,
        mailBody,
        sendmailmulti,
        sendmailsingle,
        students,
        subject
    }
}
