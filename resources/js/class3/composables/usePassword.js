import { ref, computed } from 'vue';
import axios from 'axios';
import useLang from './useLang.js';
import useNoty from './useNoty.js';    

const { ucFirst, translate } = useLang();
const { successNoty, failNoty } = useNoty();

const currentPw = ref('');
const newPw = ref('');
const repeatNewPw = ref('');

export default function usePassword() {

    const passwordErrors = computed(() => {
        const message = [];
        if (currentPw.value === '') {
            message.push(ucFirst(translate('generic.currentpasswordisrequired')));
        }
        if (newPw.value === '') {
            message.push(ucFirst(translate('generic.newpasswordisrequired')));
        }
        if (repeatNewPw.value === '') {
            message.push(ucFirst(translate('generic.repeatnewpasswordisrequired')));
        }
        if (newPw.value !== repeatNewPw.value) {
            message.push(ucFirst(translate('generic.newpasswordsdonotmatch')));
        }
        if (newPw.value.length < 8) {
            message.push(ucFirst(translate('generic.beatleast8characterslong')));
        }
        if (!newPw.value.match(/[a-z]/)) {
            message.push(ucFirst(translate('generic.containatleas1lcletter')));
        }
        if (!newPw.value.match(/[A-Z]/)) {
            message.push(ucFirst(translate('generic.containatleas1ucletter')));
        }
        if (!newPw.value.match(/['"\/\\~!@#$%^&*()_\-+=\{\}\[\]|;:<>,.?\d]/)) {
            message.push(ucFirst(translate('generic.containatleas1specornr')));
        }
        return message;
    });
    
    const changePassword = async () => {
        // API call
        try {   
            await axios.post('/api/changepassword', {
                pwcurr: currentPw.value,
                pwnw1: newPw.value,
                pwnw2: repeatNewPw.value
            });
            successNoty(translate('generic.passwordchanged'));
        } catch (error) {
            failNoty(error.response.data.message);
        }
    }

    return {
        currentPw,
        newPw,
        repeatNewPw,
        passwordErrors,
        changePassword
    }
}
