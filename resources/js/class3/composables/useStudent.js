import { ref, watch } from 'vue';
import axios from 'axios';
import useNoty from './useNoty.js';
import useLang from './useLang.js';
const { failNoty, successNoty } = useNoty();
const { translate } = useLang();

const studentId = ref(0);
const validStudentId = ref(false);
const studentDataById = ref({firstname: '', lastname: ''});
const delStudentLastname = ref('');
const validLastnameEntered = ref(false);

export default function useStudent() {
    const getStudentData = async () => {
        const response = await axios.get(`/api/students/${studentId.value}`);
        studentDataById.value = response.data;
    };
    
    const deleteStudent = async () => {
        if (validLastnameEntered.value) {   
            try {
                await axios.delete(`/api/students/full/${studentId.value}`);
                successNoty(translate('generic.studentdataremoved'));
            } catch (error) {
                failNoty(translate('generic.studentdatacouldnotberemoved'));
            }
        } else {
                failNoty(`${translate('generic.lastnameincorrect')} ${delStudentLastname.value} vs ${studentDataById.value.lastname}`);
        }
    };

    const resetStudentId = () => {
        studentId.value = 0;
        studentDataById.value = {firstname: '', lastname: ''};
        delStudentLastname.value = '';
        validStudentId.value = false;
        validLastnameEntered.value = false;
    };

    watch(studentId, () => {
        validStudentId.value = !isNaN(studentId.value) && studentId.value > 0;
    });

    watch(delStudentLastname, () => {
        validLastnameEntered.value = delStudentLastname.value.toLowerCase() === studentDataById.value.lastname.toLowerCase();
    });

    return { 
        deleteStudent, 
        getStudentData,
        resetStudentId,
        studentId,
        validStudentId,
        studentDataById,
        delStudentLastname,
        validLastnameEntered
    };
}
