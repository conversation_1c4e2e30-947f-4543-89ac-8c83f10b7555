import { computed, ref, watch } from 'vue';
import axios from 'axios';
import useNoty from './useNoty.js';
import useValidation from './useValidation.js';
import useLang from './useLang.js';
import useConversion from './useConversion.js';
import useEmailLog from './useEmailLog.js';

const { isValidEmail } = useValidation();
const { failNoty, successNoty } = useNoty();
const { translate } = useLang();
const { convertBytesToHumanReadable } = useConversion();

const cbStudents = ref(false);
const cbStaff = ref(false);
const sbGroupFilter = ref('none');
const sbCourseGroup = ref([]);
const sbStudentGroup = ref([]);

const extraEmailAddressesRawString = ref('');
const extraEmailAddresses = ref([]);
const emailType = ref('promotions');
const studentEmailAddressesAsString = ref('');
const staffEmailAddressesAsString = ref('');
const nrOfStudentEmails = ref(0);
const nrOfStaffEmails = ref(0);

const mailSubject = ref('');
const mailBody = ref('');
const hasMailConceptInLocalStorage = ref(false);
const imagesToUpload = ref([]);
const uploadedImagesResponse = ref({});
const uploadMessage = ref('');

const { getEmailLog } = useEmailLog();

// constants
const maxImageSize = 1000000; // max size of 1 image
const maxTotalImagesSize = 8000000;
const maxEmailStringDisplaySize = 400; // max nr of chars to display in popover before displaying ellipsis

export default function useEmailContacts () {
    // get all contacts with email
    const getEmailAddresses = async () => {
        const response = await axios.get(
            `/api/contacts/email/count?${filterString.value}`
        );
        nrOfStudentEmails.value = response.data.students;
        nrOfStaffEmails.value = response.data.staff;
        studentEmailAddressesAsString.value = Array.isArray(
            response.data?.studentAddresses
        )
            ? response.data.studentAddresses.join(', ')
            : [];
        if (
            studentEmailAddressesAsString.value.length > maxEmailStringDisplaySize
        ) {
            studentEmailAddressesAsString.value =
        studentEmailAddressesAsString.value.substring(
            0,
            maxEmailStringDisplaySize
        ) +
        '<br>... (' +
        translate('generic.more') +
        ')';
        }
        staffEmailAddressesAsString.value = Array.isArray(
            response.data?.staffAddresses
        )
            ? response.data?.staffAddresses.join(', ')
            : [];
        if (staffEmailAddressesAsString.value.length > maxEmailStringDisplaySize) {
            staffEmailAddressesAsString.value =
        staffEmailAddressesAsString.value.substring(
            0,
            maxEmailStringDisplaySize
        ) +
        '<br>... (' +
        translate('generic.more') +
        ')';
        }
    };
    /**
   * query string to be used in the api call
   * @type {ComputedRef<string>}
   */
    const filterString = computed(() => {
        let retString = `students=${cbStudents.value}&staff=${cbStaff.value}`;
        retString += `&email_type=${emailType.value}`;
        retString += `&course_group=${sbCourseGroup.value.join(',')}`;
        retString += `&student_group=${sbStudentGroup.value.join(',')}`;
        // coursegroup and studentgroup is extra filtering for students
        return retString;
    });

    /**
   * Watch for changes in the filter string, and get the email addresses
   * This needs to be executed after the filterString is updated, which is e.g. after the course groups array is updated.
   * This way the sequence of events is correctly preserved
   */
    watch(filterString, () => {
        getEmailAddresses();
    });

    watch(
        [cbStudents, cbStaff, emailType, sbCourseGroup, sbStudentGroup],
        () => {
            getEmailAddresses();
            hasMailConceptInLocalStorage.value =
        localStorage.getItem('mailConcept') !== null;
        },
        { immediate: true }
    );

    /**
   * check if the entered email addresses are valid, only use valid ones
   */
    watch(extraEmailAddressesRawString, () => {
        extraEmailAddresses.value = [];
        const emaiList = extraEmailAddressesRawString.value.split(',');
        emaiList.forEach((email) => {
            const trimmedEmail = email.trim();
            if (isValidEmail(trimmedEmail)) {
                extraEmailAddresses.value.push(trimmedEmail);
            }
        });
    });
    const unselectCourseGroup = (id) => {
        sbCourseGroup.value.splice(sbCourseGroup.value.indexOf(id), 1);
    };
    const unselectStudentGroup = (id) => {
        sbStudentGroup.value.splice(sbStudentGroup.value.indexOf(id), 1);
    };
    const sendable = computed(() => {
        const hasEmailAddresses =
      nrOfStudentEmails.value > 0 ||
      nrOfStaffEmails.value > 0 ||
      extraEmailAddresses.value.length > 0;
        return (
            (cbStudents.value ||
        cbStaff.value ||
        (!cbStudents.value &&
          !cbStaff.value &&
          extraEmailAddresses.value.length > 0)) &&
      hasEmailAddresses &&
      mailSubject.value.length > 0 &&
      mailBody.value.length > 0
        );
    });

    const saveConceptToLocalStorage = () => {
        localStorage.setItem(
            'mailConcept',
            JSON.stringify({
                cbStudents: cbStudents.value,
                cbStaff: cbStaff.value,
                sbCourseGroup: sbCourseGroup.value,
                sbStudentGroup: sbStudentGroup.value,
                sbGroupFilter: sbGroupFilter.value,
                emailType: emailType.value,
                extraEmailAddresses: extraEmailAddressesRawString.value,
                mailBody: mailBody.value,
                mailSubject: mailSubject.value,
                uploadedImages: uploadedImagesResponse.value
            })
        );
        hasMailConceptInLocalStorage.value = true;
        successNoty('Concept saved', '', 2000);
    };

    const getConceptFromLocalStorage = () => {
        const concept = JSON.parse(localStorage.getItem('mailConcept'));
        cbStudents.value = concept.cbStudents;
        cbStaff.value = concept.cbStaff;
        sbGroupFilter.value = concept.sbGroupFilter;
        emailType.value = concept.emailType;
        extraEmailAddressesRawString.value = concept.extraEmailAddresses;
        mailBody.value = concept.mailBody;
        mailSubject.value = concept.mailSubject;
        uploadedImagesResponse.value = concept.uploadedImages;
        setTimeout(() => {
            sbCourseGroup.value = concept.sbCourseGroup;
            sbStudentGroup.value = concept.sbStudentGroup;
        }, 1500);
        console.log(uploadedImagesResponse.value);
        successNoty('Concept restored', '', 2000);
    };

    const removeConceptFromLocalStorage = () => {
        localStorage.removeItem('mailConcept');
        hasMailConceptInLocalStorage.value = false;
        successNoty('Concept removed');
    };

    const previewFiles = async (event) => {
        imagesToUpload.value = [];
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        // iterate fileList to validate and add to imagesToUpload
        let totalFileSize = 0;
        Array.from(event.target.files).forEach((file) => {
            if (file.size > maxImageSize) {
                failNoty(
                    `File ${file.name} is too big! Max = ${convertBytesToHumanReadable(
                        maxImageSize
                    )}`
                );
                return;
            }
            // correct type is image/jpeg, image/png, image/gif
            if (!allowedTypes.includes(file.type)) {
                failNoty(`File ${file.name} is not an image!`);
                return;
            }
            totalFileSize += file.size;
            imagesToUpload.value.push(file);
        });
        if (totalFileSize > maxTotalImagesSize) {
            failNoty(
                `Total size of images is too big! Max = ${convertBytesToHumanReadable(
                    maxTotalImagesSize
                )}`
            );
            imagesToUpload.value = [];
            return;
        }
        // All a-ok, set uploadMessage and start uploading
        uploadMessage.value = `${imagesToUpload.value.length} images uploading...`;
        await uploadImages();
    };

    const uploadImages = async () => {
        const formData = new FormData();
        imagesToUpload.value.forEach((image) => {
            formData.append('images[]', image);
        });
        try {
            const response = await axios.post(
                '/api/contacts/email/upload',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                }
            );
            uploadMessage.value = `${response.data.message}`;
            uploadedImagesResponse.value = response.data;
        } catch (err) {
            failNoty(err);
        }
    };

    /**
   * insert the image url in the mail body
   * @param url
   */
    const insertImageUrl = (url) => {
        const imageToInsert = `<img src="${url}" />`;
        mailBody.value += imageToInsert;
    };

    const sendEmail = () => {
        axios
            .post('/api/contacts/email', {
                cbStudents: cbStudents.value,
                cbStaff: cbStaff.value,
                courseGroup: sbCourseGroup.value,
                studentGroup: sbStudentGroup.value,
                emailType: emailType.value,
                extraEmailAddresses: extraEmailAddresses.value,
                mailBody: mailBody.value,
                mailSubject: mailSubject.value
            })
            .then((response) => {
                successNoty(response.data.message);
            })
            .catch((err) => {
                failNoty(err);
            })
            .finally(() => {
                getEmailLog();
            });
    };
    /**
   * reset the course group and student group on any filter change
   */
    watch(sbGroupFilter, () => {
        sbCourseGroup.value = [];
        sbStudentGroup.value = [];
    });

    return {
        cbStaff,
        cbStudents,
        emailType,
        extraEmailAddresses,
        extraEmailAddressesRawString,
        filterString,
        getConceptFromLocalStorage,
        getEmailAddresses,
        hasMailConceptInLocalStorage,
        imagesToUpload,
        insertImageUrl,
        mailBody,
        mailSubject,
        maxEmailStringDisplaySize,
        maxImageSize,
        maxTotalImagesSize,
        nrOfStaffEmails,
        nrOfStudentEmails,
        previewFiles,
        removeConceptFromLocalStorage,
        saveConceptToLocalStorage,
        sbCourseGroup,
        sbGroupFilter,
        sbStudentGroup,
        sendable,
        sendEmail,
        staffEmailAddressesAsString,
        studentEmailAddressesAsString,
        unselectCourseGroup,
        unselectStudentGroup,
        uploadedImagesResponse,
        uploadImages,
        uploadMessage
    };
}
