import { computed, ref } from "vue";
import axios from 'axios';
import useNoty from "./useNoty.js";
import useLang from "./useLang.js";

const { failNoty, successNoty } = useNoty();
const { translate, translateChoice, ucFirst } = useLang();

const busy = ref(false);
const libraries = ref([]);
// Edits
const libraryIdToDelete = ref(null);
const activeLibraryId = ref(null);     // lib that we are currently editing or managing docs or shares
                                       // so we can highlight it in the list
const currentChangeAction = ref(null); // one of: 'edit', 'manageContents', 'manageShares'
// Shares
const shareWholeSchool = ref(true);
const shareCoursesSelected = ref([]);    // this will contain an array of selected id's
const shareCourseGroupsSelected = ref([]);
const shareStudentGroupsSelected = ref([]);
const shareStudentsSelected = ref([]);

export default function useLibraries() {

    const getLibraries = async () => {
        busy.value = true;
        try {
            const response = await axios.get('/api/libraries');
            libraries.value = response.data;

        } catch (error) {
            failNoty(error);
        } finally {
            busy.value = false;
        }
    }

    const emptyLibrary = () => {
        activeLibraryId.value = 0;
        currentChangeAction.value = "edit";
    }

    const removeLibrary = async () => {
        try {
            busy.value = true;
            await axios.delete(`/api/libraries/${libraryIdToDelete.value}`);
            activeLibraryId.value = 0;
            currentChangeAction.value = "";
            await getLibraries();
            successNoty(ucFirst(translateChoice('generic.libraries', 1)) + ' ' + translate('generic.deleted'));
        } catch (error) {
            failNoty(error);
        } finally {
            busy.value = false;
        }
    }

    const libraryToEdit = computed(() => {
        if (activeLibraryId.value === 0) {
            return {
                id: 0,
                label: '',
                share_with_whole_school: false
            };
        } else {
            return libraries.value.find(library => library.id === activeLibraryId.value);
        }
    });

    const libraryToManageContents = computed(() => {
        return libraries.value.find(library => library.id === activeLibraryId.value);
    });

    const libraryToManageShares = computed(() => {
        return libraries.value.find(library => library.id === activeLibraryId.value);
    });

    /**
     * save the basic fields of the library (label, description)
     */
    const saveLibrary = async () => {
        try {
            busy.value = true;
            let url, method, message;

            if (libraryToEdit.value.id === 0) {
                url = '/api/libraries';
                method = 'post';
                message = ucFirst(translateChoice('generic.libraries', 1)) + ' ' + translate('generic.created');
            } else {
                url = `/api/libraries/${libraryToEdit.value.id}`;
                method = 'put';
                message = ucFirst(translateChoice('generic.libraries', 1)) + ' ' + translate('generic.updated');
            }
            await axios[method](url, libraryToEdit.value);
            await getLibraries();
            successNoty(message);
        } catch (error) {
            failNoty(error);
        } finally {
            busy.value = false;
        }
    }

    /**
     * save to whome the shares of the library are given
     * that may be a course, a course group, student, a student group or a whole school
     */
    const saveSharesLibrary = async () => {
        try {
            busy.value = true;
            const data = {
                shareWholeSchool: shareWholeSchool.value,
                shareCourses: shareCoursesSelected.value,
                shareCourseGroups: shareCourseGroupsSelected.value,
                shareStudents: shareStudentsSelected.value,
                shareStudentGroups: shareStudentGroupsSelected.value
            };
            await axios.put(`/api/libraries/${libraryToEdit.value.id}/shares`, data);
            await getLibraries();
            successNoty(ucFirst(translate('generic.librarysharing')) + ' ' + translate('generic.updated'));
        } catch (error) {
            failNoty(error);
        } finally {
            busy.value = false;
        }
    }

    return {
        activeLibraryId,
        busy,
        currentChangeAction,
        getLibraries,
        emptyLibrary,
        libraries,
        libraryIdToDelete,
        libraryToEdit,
        libraryToManageContents,
        libraryToManageShares,
        removeLibrary,
        saveLibrary,
        saveSharesLibrary,
        shareCourseGroupsSelected,
        shareCoursesSelected,
        shareStudentGroupsSelected,
        shareStudentsSelected,
        shareWholeSchool
    }
}
