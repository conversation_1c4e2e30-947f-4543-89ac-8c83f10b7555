import { computed, ref } from "vue";
import axios from 'axios';
import useLang from "./useLang.js";
import useLibraries from "./useLibraries.js";

const documents = ref([]);
const documentSearchKey = ref("");
const { failNoty } = useLang();
const { getLibraries } = useLibraries();

export default function useDocuments() {
    const getDocuments = async (libraryId) => {
        const response = await axios.get(`/api/documents/${libraryId}`);
        documents.value = response.data;
    };

    const filteredDocuments = computed(() => {
        return documents.value.filter(doc => doc.label.toLowerCase().includes(documentSearchKey.value.toLowerCase()));
    });

    const addDocumentToLibrary = async (libraryId, docId) => {
        try {
            await axios.put(`/api/attachdoctolib/${libraryId}/${docId}`);
            documentSearchKey.value = '';
            await getLibraries();
        } catch (error) {
            failNoty(`${ucFirst(translate('generic.attachingdocumentfailed'))}: ${error}`);
        }
    }

    const removeDocumentFromLibrary = async (libraryId, docId) => {
        try {
            await axios.put(`/api/detachdocfromlib/${libraryId}/${docId}`);
            documentSearchKey.value = '';
            await getLibraries();
        } catch (error) {
            failNoty(`${ucFirst(translate('generic.detachingdocumentfailed'))}: ${error}`);
        }
    }

    return {
        addDocumentToLibrary,
        documentSearchKey,
        filteredDocuments,
        getDocuments,
        removeDocumentFromLibrary
    };
}
