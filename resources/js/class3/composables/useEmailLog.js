import { computed, ref } from 'vue';
import axios from 'axios';

const emailLog = ref([]);
const fetchTimer = ref(null);

export default function useEmailLog () {
    const fetchEmailLog = async () => {
        const response = await axios.get('/api/emaillogentries');
        emailLog.value = response.data;
    };

    const listHasQueuedEmails = computed(() => {
        return emailLog.value.some((entry) => entry.status === 'queued');
    });

    const getEmailLog = async () => {
        await fetchEmailLog();
        // as long as we have queued emails, keep fetching the email log to auto update the list
        if (listHasQueuedEmails.value) {
            fetchTimer.value = setInterval(async () => {
                await fetchEmailLog();
                if (!listHasQueuedEmails.value) {
                    clearInterval(fetchTimer.value);
                }
            }, 5000);
        } else {
            if (fetchTimer.value) {
                clearInterval(fetchTimer.value);
            }
        }
    };

    const deleteFromEmailLog = async (id) => {
        await axios.delete(`/api/emaillogentries/${id}`);
        await fetchEmailLog();
    };

    return {
        deleteFromEmailLog,
        emailLog,
        getEmailLog
    };
}
