<div class="col-md-12">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">{{ucfirst(trans('generic.deletestudent'))}}</h4>
        </div>
        <div class="panel-body">
            <label>
                {{ucfirst(trans('generic.uniquestudentidentifier'))}} (*)
                <i class="fa fa-question-circle" title="{{ucfirst(trans('generic.studenteditformtopright'))}}" data-toggle="tooltip"></i>
            </label>
            <input class="form-control" name="studentId" v-model="studentId"/>

            <span v-if="validStudentId" class="btn btn-danger" data-toggle="modal" data-target="#confirm-remove-student" @click="fillPopupFields()">
                {{ucfirst(trans('generic.deletethisstudent'))}}
            </span>
        </div>
    </div>
</div>

{{-- modal for completely deleting a student, handle with care! --}}
@if(Auth::user()->userIsA('admin'))
    <modal  closetext="{{ucfirst(trans('generic.close'))}}"
            popup-title="{{ucfirst(trans('generic.deletestudent'))}}"
            modal-id="confirm-remove-student"
            @closebtnclick="resetstudentid"
    >
        <p>{{trans('generic.studentsfirstnameis')}}: @{{studentDataById.firstname}}</p>
        <p>
            {{trans('generic.pleaseenterlastnameascheck')}}:
            <input class="form-control" v-model="delStudentLastname"/>
        </p>
        <button slot="okbutton" type="button" v-if="validLastnameEntered"
                class="btn btn-danger" data-dismiss="modal"
                @click.prevent="deleteStudent()">{{ ucfirst(trans('generic.deletethisstudent')) }}
        </button>
    </modal>
@endif
