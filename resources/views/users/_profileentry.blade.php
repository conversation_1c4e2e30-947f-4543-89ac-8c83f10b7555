<div class="panel panel-default">
    <div class="panel-heading">
        <h4 class="panel-title">{{ucfirst(trans('generic.profile'))}}</h4>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-md-6 col-sm-8 col-xs-12">
                @if(!empty(Auth::user()->avatar))
                    <img src="{{Auth::user()->avatar}}" height="100px"/>
                @else
                    <img src="/images/no-profile.png" height="100px"/>
                @endif
            </div>
            <div class="col-md-6 col-sm-4 col-xs-12">
                {!! Form::label("preferred_language",
                        ucfirst(trans('generic.preferredlanguage')),
                        ["style" => 'padding-right: 1rem;'])
                !!}
                <br>
                @php
                    $prefLang = Auth::user()->preferred_language ?: Auth::user()->domain->language;
                @endphp
                <label data-toggle="tooltip" title="nederlands">
                    {!! Form::radio("preferred_language", 'nl', ($prefLang==='nl' ? ['selected' => 'selected'] : [] )) !!}
                    <img src="/images/nl.svg" class="lang-img" />
                </label>
                <label data-toggle="tooltip" title="engels">
                    {!! Form::radio("preferred_language", 'en', ($prefLang==='en' ? ['selected' => 'selected'] : [] )) !!}
                    <img src="/images/gb.svg" class="lang-img" />
                </label>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group{{ $errors->has('name') ? ' has-error' : '' }}">
                    {!! Form::label("name", ucfirst(trans('generic.name')) . ' (' . trans('generic.mandatory'). ')') !!}
                    {!! Form::text("name", Auth::user()->name, ['class' => 'form-control']) !!}
                    @if ($errors->has('name'))
                        <span class="help-block"><strong>{{ $errors->first('name') }}</strong></span>
                    @endif
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }}">
                    {!! Form::label("email", ucfirst(trans('generic.email')) . ' (' . trans('generic.mandatory'). ')') !!}
                    {!! Form::text("email", Auth::user()->email, ['class' => 'form-control']) !!}
                    @if ($errors->has('email'))
                        <span class="help-block"><strong>{{ $errors->first('email') }}</strong></span>
                    @endif
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group{{ $errors->has('avatar') ? ' has-error' : '' }}">
                    {!! Form::label("avatar", ucfirst(trans('generic.profilephoto'))) !!}
                    {!! Form::text("avatar", Auth::user()->avatar, ['class' => 'form-control', 'placeholder'=>trans('generic.weburlofyourprofilephoto')]) !!}
                    @if ($errors->has('avatar'))
                        <span class="help-block"><strong>{{ $errors->first('avatar') }}</strong></span>
                    @endif
                </div>
            </div>
        </div>

        <div class="form-group">
            {!! Form::submit(ucfirst(trans('generic.save')), ['class'=>'btn btn-primary']) !!}
            @if(!Auth::user()->userIsA('tutor') && Auth::user()->userIsA('admin'))
                <button @click.prevent='' v-if="!useristutor"
                        class="btn btn-default" data-toggle="modal" data-target="#settotutorpopup">
                    {{ucfirst(trans('generic.givemetutorrights'))}}
                </button>
            @endif
        </div>

    </div>
</div>
