{!! Form::open(['route'=>'changepassword', "class"=>"form-horizontal", "role" => "form"]) !!}

<div class="modal-body">
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group{{ $errors->has('pwcurr') ? ' has-error' : '' }}">
                {!! Form::label("pwcurr", ucfirst(trans('generic.currentpassword'))) !!}
                <input type="password" class="form-control" id="pwcurr" name="pwcurr"/>
                @if ($errors->has('pwcurr'))
                    <span class="help-block"><strong>{{ $errors->first('pwcurr') }}</strong></span>
                @endif
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <small>
                {{ucfirst(trans('generic.yournewpasswordneedsto'))}}:
                <ul>
                    <li>{{trans('generic.beatleast8characterslong')}}</li>
                    <li>{{trans('generic.containatleas1lcletter')}}</li>
                    <li>{{trans('generic.containatleas1ucletter')}}</li>
                    <li>
                        {{trans('generic.containatleas1specornr')}}
                        <ul>
                            <li>{{trans('generic.specialchars')}}</li>
                        </ul>
                    </li>
                </ul>
                {{ucfirst(trans('generic.rememberlongisstrong'))}}.
            </small>
            <br/>&nbsp;
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group{{ $errors->has('pwnw1') ? ' has-error' : '' }}">
                {!! Form::label("pwnw1", ucfirst(trans('generic.newpassword'))) !!}
                <input type="password" value="{{old('pwnw1')}}" class="form-control" id="pwnw1" name="pwnw1"/>
                @if ($errors->has('pwnw1'))
                    <span class="help-block"><strong>{{ $errors->first('pwnw1') }}</strong></span>
                @endif
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <div class="form-group{{ $errors->has('pwnw2') ? ' has-error' : '' }}">
                {!! Form::label("pwnw2", ucfirst(trans('generic.repeatnewpassword'))) !!}
                <input type="password" value="{{old('pwnw2')}}" class="form-control" id="pwnw2" name="pwnw2"/>
                @if ($errors->has('pwnw2'))
                    <span class="help-block"><strong>{{ $errors->first('pwnw2') }}</strong></span>
                @endif
            </div>
        </div>
    </div>

</div>

<div class="modal-footer">
    <div class="form-group">
        <div class="btn-group" role="group" aria-label="buttons">
            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            {!! Form::submit(ucfirst(trans('generic.changepassword')), ['class'=>'btn btn-primary']) !!}
        </div>
    </div>
</div>

{!! Form::close() !!}