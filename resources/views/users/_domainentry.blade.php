<div class="row">
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('name') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.schoolname'))}} (*)
            </label>
            {!! Form::text("name", Auth::user()->domain->name, ['class' => 'form-control']) !!}
            @if ($errors->has('name'))
                <span class="help-block"><strong>{{ $errors->first('name') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('domain_name') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.domainname'))}} (*)
            </label>
            {!! Form::text("domain_name", Auth::user()->domain->domain_name, ['class' => 'form-control']) !!}
            @if ($errors->has('domain_name'))
                <span class="help-block"><strong>{{ $errors->first('domain_name') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.schoolwebsite'))}}
            </label>
            {!! Form::text("website_url", Auth::user()->domain->website_url, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.defaultpassword'))}}
            </label>
            <input class="form-control" readonly="readonly" value="{{ Auth::user()->domain->default_password }}">
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.namecontactperson'))}}
            </label>
            {!! Form::text("contact_person_name", Auth::user()->domain->contact_person_name, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-3">&nbsp;</div>
    <div class="col-md-3">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.logourl'))}}
            </label>
            {!! Form::hidden("logo_url_org", Auth::user()->domain->logo_url, ['class' => 'form-control', "id" => "logo_url_org"]) !!}
            {!! Form::text("logo_url", null, ['class' => 'form-control', 'v-model' => 'logourl']) !!}
        </div>
    </div>
    <div class="col-md-3">
        <img :src="logourl" height="60px" />
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('address1') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.addressline1'))}} (*)
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{!! trans('generic.explainaddressfields') !!}"></i>
            </label>
            {!! Form::text("address1", Auth::user()->domain->address1, ['class' => 'form-control']) !!}
            @if ($errors->has('address1'))
                <span class="help-block"><strong>{{ $errors->first('address1') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('address2') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.addressline2'))}} (*)
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{!! trans('generic.explainaddressfields') !!}"></i>
            </label>
            {!! Form::text("address2", Auth::user()->domain->address2, ['class' => 'form-control']) !!}
            @if ($errors->has('address2'))
                <span class="help-block"><strong>{{ $errors->first('address2') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.telephone'))}}
            </label>
            {!! Form::text("telephone", Auth::user()->domain->telephone, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.email'))}} (*)
            </label>
            {!! Form::text("email", Auth::user()->domain->email, ['class' => 'form-control']) !!}
            @if ($errors->has('email'))
                <span class="help-block"><strong>{{ $errors->first('email') }}</strong></span>
            @endif
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('zip') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.zipcode'))}} (*)
            </label>
            {!! Form::text("zip", Auth::user()->domain->zip, ['class' => 'form-control']) !!}
            @if ($errors->has('zip'))
                <span class="help-block"><strong>{{ $errors->first('zip') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('city') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.city'))}} (*)
            </label>
            {!! Form::text("city", Auth::user()->domain->city, ['class' => 'form-control']) !!}
            @if ($errors->has('city'))
                <span class="help-block"><strong>{{ $errors->first('city') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('adult_threshold') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.adultthreshold'))}}
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{{trans('generic.explainadultthreshold')}}"></i>
            </label>
            {!! Form::text("adult_threshold", Auth::user()->domain->adult_threshold, ['class' => 'form-control']) !!}
            @if ($errors->has('adult_threshold'))
                <span class="help-block"><strong>{{ $errors->first('adult_threshold') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-3">
        <div class="form-group{{ $errors->has('course_tax_rate') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.coursetaxrateadults'))}}
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{{trans('generic.explaincoursetaxrateadults')}}"></i>
            </label>
            {!! Form::text("course_tax_rate", Auth::user()->domain->course_tax_rate, ['class' => 'form-control']) !!}
            @if ($errors->has('course_tax_rate'))
                <span class="help-block"><strong>{{ $errors->first('course_tax_rate') }}</strong></span>
            @endif
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group{{ $errors->has('warn_before_birthday') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.warnbeforebirthdaystudents'))}}
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{{trans('generic.explainwarnbeforebirthday')}}"></i>
            </label>
            {!! Form::text("warn_before_birthday", Auth::user()->domain->warn_before_birthday, ['class' => 'form-control']) !!}
            @if ($errors->has('warn_before_birthday'))
                <span class="help-block"><strong>{{ $errors->first('warn_before_birthday') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group{{ $errors->has('warn_before_adult') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.warnbeforeadultstudents'))}}
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{{trans('generic.explainwarnbeforeadult')}}"></i>
            </label>
            {!! Form::text("warn_before_adult", Auth::user()->domain->warn_before_adult, ['class' => 'form-control']) !!}
            @if ($errors->has('warn_before_adult'))
                <span class="help-block"><strong>{{ $errors->first('warn_before_adult') }}</strong></span>
            @endif
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group{{ $errors->has('schedule_threshold') ? ' has-error' : '' }}">
            <label>
                {{ucfirst(trans('generic.schedulethreshold'))}}
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{{trans('generic.explainschedulethreshold')}}"></i>
            </label>
            {!! Form::text("schedule_threshold", Auth::user()->domain->schedule_threshold, ['class' => 'form-control']) !!}
            @if ($errors->has('schedule_threshold'))
                <span class="help-block"><strong>{{ $errors->first('schedule_threshold') }}</strong></span>
            @endif
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.schoolratesandconditionsurl'))}}
                @if(strlen(Auth::user()->domain->rates_conditions_url) > 0)
                    <a href="{{Auth::user()->domain->rates_conditions_url}}" target="_blank">[{{ ucfirst(trans('generic.testlink'))}}]</a>
                @endif
            </label>
            {!! Form::text("rates_conditions_url", Auth::user()->domain->rates_conditions_url, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.schoolprivacystatementurl'))}}
                @if(strlen(Auth::user()->domain->privacy_url) > 0)
                    <a href="{{Auth::user()->domain->privacy_url}}" target="_blank">[{{ ucfirst(trans('generic.testlink'))}}]</a>
                @endif
            </label>
            {!! Form::text("privacy_url", Auth::user()->domain->privacy_url, ['class' => 'form-control']) !!}
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label>
                {{ucfirst(trans('generic.allowedipaddressesbroadcast'))}}
                <i data-toggle="tooltip" class="fa fa-question-circle" title="{{trans('generic.explainallowedipadd')}}"></i>
            </label>
            {!! Form::text("allowed_ip_addresses", Auth::user()->domain->allowed_ip_addresses, ['class' => 'form-control']) !!}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="form-group">
            {!! Form::submit(ucfirst(trans('generic.domain')) . " " . trans('generic.save'), ['class'=>'btn btn-primary']) !!}
        </div>
    </div>
</div>
