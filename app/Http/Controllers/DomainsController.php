<?php

namespace App\Http\Controllers;

use App\Http\Resources\DomainInfoResource;
use App\Http\Resources\StudentDomainInfoResource;
use App\Models\Domain;
use App\Http\Requests\DomainsRequest;
use App\Models\Student;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DomainsController extends Controller {
    /**
     * Updates the domain data.
     *
     * @param DomainsRequest $request The domains request object.
     *
     * @return \Illuminate\Http\JsonResponse Returns a JSON response with a 'message' key indicating that the data has been saved.
     */
    public function updatedomaininfo(DomainsRequest $request)
    {
        $id = Auth::user()->domain_id;
        Log::info("Saving Domain data");

        $ipAddresses = $request->allowedIpAddresses;
        // cleanup the IP addresses in case there are illegal characters in there
        $parts = explode(",", $ipAddresses);
        // make sure there are no spaces in the IP addresses
        $parts = array_map('trim', $parts);
        // remove invalid IP addresses
        $parts = array_map(function($ip) {
            return filter_var($ip, FILTER_VALIDATE_IP);
        }, $parts);
        // remove empty elements
        $parts = array_filter($parts);
        // remove duplicates
        $parts = array_unique($parts);
        // turn into a string again
        $ipAddresses = implode(",", $parts);

        $domain = Domain::findOrFail($id);
        $domain->domain_name = $request->domainName;
        $domain->website_url = $request->websiteUrl;
        $domain->logo_url = $request->logoUrl;
        $domain->language = $request->language;
        $domain->rates_conditions_url = $request->ratesConditionsUrl;
        $domain->privacy_url = $request->privacyUrl;
        $domain->name = $request->name;
        $domain->address1 = $request->address1;
        $domain->address2 = $request->address2 ?? '';
        $domain->zip = $request->zip;
        $domain->city = $request->city;
        $domain->telephone = $request->telephone;
        $domain->email = $request->email;
        $domain->contact_person_name = $request->contactPersonName;
        $domain->adult_threshold = $request->adultThreshold;
        $domain->course_tax_rate = $request->courseTaxRate;
        $domain->schedule_threshold = $request->scheduleThreshold;
        $domain->warn_before_birthday = $request->warnBeforeBirthday;
        $domain->warn_before_adult = $request->warnBeforeAdult;
        $domain->allowed_ip_addresses = $ipAddresses;
        $domain->save();
        // back to the edit screen
        //return redirect()->route('profileedit')->with('message', ucfirst(trans("generic.datasaved")));
        // this is no longer a web route, so we need to return a JSON response
        return response()->json(['message' => 'Data saved']);
    }

    /**
     * Retrieves (almost) all the domain data.
     * Meaning: some things are stored in the domain that should not be available to the end user
     *
     * @return DomainInfoResource Returns the domain data.
     */
    public function getdomaininfo()
    {
        Log::info("Getting domain data");
        $domain = Auth::user()->domain;
        return DomainInfoResource::make($domain);
    }

    /**
     * Retrieves the domain data based on the access token.
     * @return StudentDomainInfoResource | \Illuminate\Http\JsonResponse
     */
    public function getdomaininfobyaccesstoken()
    {
        Log::info("Getting domain data by access token");
        $student = Student::query()->where('accesstoken', request('accesstoken'))->first();
        if (!$student) {
            Log::warning("Student accesstoken not valid");
            return response()->json(['message' => 'Invalid access token'], 401);
        }
        if (!$student->has_access) {
            Log::warning("Student does not have access");
            return response()->json(['message' => 'No access'], 403);
        }
        $domain = $student->domain;
        return StudentDomainInfoResource::make($domain);
    }

    /**
     * Opens the domain settings page.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View Returns the view for the domain settings page.
     */
    public function domainSettings()
    {
        // open the domain settings page
        return view('domains.settings');
    }
}
