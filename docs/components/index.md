# Components

## Class components
- [Courses](Class/courses/index)
- [Domain](Class/domain/index)
- [Email](Class/email/index)
- [Location](Class/location/index)
- [Planning](Class/planning/index)
- [Profile](Class/profile/index)
- [Registration](Class/registration/index)
- [SchoolYears](Class/schoolyears/index)
- [Students](Class/students/index)
- [StudentGroups](Class/studentgroups/index)

## ClassY components
- [Library](ClassY/Library/index)
